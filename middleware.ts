import createMiddleware from 'next-intl/middleware';
import { routing } from './src/i18n/routing';
import { NextRequest, NextResponse } from 'next/server';

const intlMiddleware = createMiddleware(routing);

export default function middleware(request: NextRequest) {
  // Get the pathname and extract locale from URL
  const pathname = request.nextUrl.pathname;
  const pathLocale = pathname.split('/')[1];

  // Check if there's a NEXT_LOCALE cookie that conflicts with the URL locale
  const nextLocaleCookie = request.cookies.get('NEXT_LOCALE');

  console.log('Middleware - pathname:', pathname);
  console.log('Middleware - pathLocale:', pathLocale);
  console.log('Middleware - nextLocaleCookie:', nextLocaleCookie?.value);

  if (
    nextLocaleCookie &&
    pathLocale &&
    pathLocale !== nextLocaleCookie.value &&
    routing.locales.includes(pathLocale)
  ) {
    console.log('Middleware - clearing conflicting NEXT_LOCALE cookie');
    // Clear the conflicting NEXT_LOCALE cookie
    const response = intlMiddleware(request);
    if (response) {
      response.cookies.delete('NEXT_LOCALE');
      return response;
    }
  }

  return intlMiddleware(request);
}

export const config = {
  matcher: ['/', '/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
