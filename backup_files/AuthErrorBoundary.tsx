'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { authStorage } from '@/lib/auth-storage';

interface AuthErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface AuthErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class AuthErrorBoundary extends React.Component<
  AuthErrorBoundaryProps,
  AuthErrorBoundaryState
> {
  constructor(props: AuthErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(
    error: Error,
  ): Partial<AuthErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Auth Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });

    // Log to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to error reporting service (e.g., Sentry)
    }
  }

  retry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent error={this.state.error!} retry={this.retry} />
        );
      }

      return (
        <DefaultAuthErrorFallback
          error={this.state.error!}
          retry={this.retry}
        />
      );
    }

    return this.props.children;
  }
}

interface AuthErrorFallbackProps {
  error: Error;
  retry: () => void;
}

function DefaultAuthErrorFallback({ error, retry }: AuthErrorFallbackProps) {
  const t = useTranslations('Auth.Errors');
  const router = useRouter();

  const getErrorMessage = (error: Error) => {
    // Map common auth errors to user-friendly messages
    if (error.message.includes('Invalid login credentials')) {
      return t('invalidCredentials');
    }
    if (error.message.includes('Email not confirmed')) {
      return t('emailNotVerified');
    }
    if (error.message.includes('User not found')) {
      return t('userNotFound');
    }
    if (error.message.includes('Network')) {
      return t('networkError');
    }
    if (error.message.includes('Session')) {
      return t('sessionExpired');
    }

    return t('unknownError');
  };

  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            {t('title')}
          </h2>
          <p className="mt-2 text-sm text-gray-600">{getErrorMessage(error)}</p>

          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4 text-left">
              <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                Technical Details
              </summary>
              <pre className="mt-2 overflow-auto rounded bg-gray-100 p-2 text-xs text-gray-600">
                {error.stack}
              </pre>
            </details>
          )}
        </div>

        <div className="space-y-4">
          <button
            onClick={retry}
            className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
          >
            {t('retry')}
          </button>

          <button
            onClick={handleReload}
            className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
          >
            {t('reload')}
          </button>

          <button
            onClick={handleGoHome}
            className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
          >
            {t('goHome')}
          </button>
        </div>
      </div>
    </div>
  );
}

// Hook for handling auth errors in components
export function useAuthErrorHandler() {
  const router = useRouter();

  const handleAuthError = (error: any) => {
    console.error('Auth error:', error);

    // Handle specific error cases
    if (error?.message?.includes('Session expired')) {
      // Clear secure session storage and redirect to login
      authStorage.clear();
      router.push('/auth/login');
      return;
    }

    if (error?.message?.includes('Email not confirmed')) {
      // Redirect to email verification
      router.push('/auth/verify-email');
      return;
    }

    if (error?.message?.includes('Invalid login credentials')) {
      // Show error message but stay on current page
      return;
    }

    // For other errors, throw to be caught by error boundary
    throw error;
  };

  return { handleAuthError };
}

// Component for handling email verification edge cases
export function EmailVerificationHandler({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const [verificationStatus, setVerificationStatus] = React.useState<
    'checking' | 'verified' | 'unverified'
  >('checking');

  React.useEffect(() => {
    checkEmailVerification();
  }, []);

  const checkEmailVerification = async () => {
    try {
      // Handle dynamic import with specific error handling
      let supabase;
      try {
        const supabaseModule = await import('@/lib/supabase');
        supabase = supabaseModule.supabase;

        if (!supabase) {
          throw new Error('Supabase client not available from imported module');
        }
      } catch (importError) {
        console.error('Failed to import Supabase module:', importError);
        setVerificationStatus('unverified');
        return;
      }

      // Handle user retrieval with specific error handling
      let user;
      try {
        const {
          data: { user: userData },
          error: userError,
        } = await supabase.auth.getUser();

        if (userError) {
          throw new Error(`Supabase auth error: ${userError.message}`);
        }

        user = userData;
      } catch (authError) {
        console.error('Failed to retrieve user from Supabase:', authError);
        setVerificationStatus('unverified');
        return;
      }

      // Check user verification status
      if (!user) {
        setVerificationStatus('unverified');
        return;
      }

      if (user.email_confirmed_at) {
        setVerificationStatus('verified');
      } else {
        setVerificationStatus('unverified');
      }
    } catch (error) {
      // Catch any other unexpected errors
      console.error('Unexpected error during email verification check:', error);
      setVerificationStatus('unverified');
    }
  };

  if (verificationStatus === 'checking') {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-indigo-600"></div>
          <p className="mt-4 text-sm text-gray-600">
            Checking email verification...
          </p>
        </div>
      </div>
    );
  }

  if (verificationStatus === 'unverified') {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
              <svg
                className="h-6 w-6 text-yellow-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 7.89a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Email Verification Required
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Please verify your email address to continue.
            </p>
            <button
              onClick={() => router.push('/auth/verify-email')}
              className="mt-4 inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
            >
              Verify Email
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
