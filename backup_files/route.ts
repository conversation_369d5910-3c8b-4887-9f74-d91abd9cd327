import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { AuthResponse, OrganizationRole } from '@/types/auth';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SECRET_KEY!,
);

// Get organization members
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const organizationId = id;

    // Get user from authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Authorization required',
        },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Invalid authorization',
        },
        { status: 401 },
      );
    }

    if (!organizationId) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Organization ID is required',
        },
        { status: 400 },
      );
    }

    // Check if user has access to this organization
    const { data: userAccess, error: accessError } = await supabase
      .from('user_organizations')
      .select('role')
      .eq('user_id', user.id)
      .eq('organization_id', organizationId)
      .single();

    if (accessError || !userAccess) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Access denied to this organization',
        },
        { status: 403 },
      );
    }

    // Get organization members
    const { data: members, error: membersError } = await supabase
      .from('user_organizations')
      .select(
        `
        id,
        role,
        joined_at,
        invited_by,
        invited_at,
        user_profile:user_profiles(
          id,
          email,
          full_name,
          phone,
          avatar_url,
          created_at
        ),
        invited_by_profile:user_profiles!invited_by(
          full_name,
          email
        )
      `,
      )
      .eq('organization_id', organizationId)
      .order('joined_at', { ascending: true });

    if (membersError) {
      console.error('Failed to fetch members:', membersError);
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Failed to fetch organization members',
        },
        { status: 500 },
      );
    }

    return NextResponse.json<AuthResponse>({
      success: true,
      data: members || [],
    });
  } catch (error) {
    console.error('Get members error:', error);
    return NextResponse.json<AuthResponse>(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}

// Update member role
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const organizationId = id;
    const { user_id, role } = await request.json();

    // Get user from authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Authorization required',
        },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Invalid authorization',
        },
        { status: 401 },
      );
    }

    // Validate required fields
    if (!organizationId || !user_id || !role) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Organization ID, user ID, and role are required',
        },
        { status: 400 },
      );
    }

    // Validate role
    const validRoles: OrganizationRole[] = [
      'owner',
      'admin',
      'member',
      'accountant',
      'viewer',
    ];
    if (!validRoles.includes(role)) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Invalid role',
        },
        { status: 400 },
      );
    }

    // Check if current user has permission to update roles
    const { data: currentUserRole, error: roleError } = await supabase
      .from('user_organizations')
      .select('role')
      .eq('user_id', user.id)
      .eq('organization_id', organizationId)
      .single();

    if (
      roleError ||
      !currentUserRole ||
      !['owner', 'admin'].includes(currentUserRole.role)
    ) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Insufficient permissions to update member roles',
        },
        { status: 403 },
      );
    }

    // Prevent users from changing their own owner role
    if (
      user_id === user.id &&
      currentUserRole.role === 'owner' &&
      role !== 'owner'
    ) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Cannot change your own owner role',
        },
        { status: 400 },
      );
    }

    // Prevent non-owners from assigning owner role
    if (role === 'owner' && currentUserRole.role !== 'owner') {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Only owners can assign owner role',
        },
        { status: 403 },
      );
    }

    // Update the member role
    const { error: updateError } = await supabase
      .from('user_organizations')
      .update({
        role,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', user_id)
      .eq('organization_id', organizationId);

    if (updateError) {
      console.error('Failed to update member role:', updateError);
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Failed to update member role',
        },
        { status: 500 },
      );
    }

    return NextResponse.json<AuthResponse>({
      success: true,
      message: 'Member role updated successfully',
    });
  } catch (error) {
    console.error('Update member role error:', error);
    return NextResponse.json<AuthResponse>(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}

// Remove member from organization
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const organizationId = id;
    const { searchParams } = new URL(request.url);
    const userIdToRemove = searchParams.get('user_id');

    // Get user from authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Authorization required',
        },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Invalid authorization',
        },
        { status: 401 },
      );
    }

    if (!organizationId || !userIdToRemove) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Organization ID and user ID are required',
        },
        { status: 400 },
      );
    }

    // Check if current user has permission to remove members
    const { data: currentUserRole, error: roleError } = await supabase
      .from('user_organizations')
      .select('role')
      .eq('user_id', user.id)
      .eq('organization_id', organizationId)
      .single();

    if (
      roleError ||
      !currentUserRole ||
      !['owner', 'admin'].includes(currentUserRole.role)
    ) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Insufficient permissions to remove members',
        },
        { status: 403 },
      );
    }

    // Get the role of the user being removed
    const { data: targetUserRole, error: targetRoleError } = await supabase
      .from('user_organizations')
      .select('role')
      .eq('user_id', userIdToRemove)
      .eq('organization_id', organizationId)
      .single();

    if (targetRoleError || !targetUserRole) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'User is not a member of this organization',
        },
        { status: 404 },
      );
    }

    // Prevent removing the last owner
    if (targetUserRole.role === 'owner') {
      const { data: ownerCount, error: countError } = await supabase
        .from('user_organizations')
        .select('id', { count: 'exact' })
        .eq('organization_id', organizationId)
        .eq('role', 'owner');

      if (countError || (ownerCount && ownerCount.length <= 1)) {
        return NextResponse.json<AuthResponse>(
          {
            success: false,
            error: 'Cannot remove the last owner of the organization',
          },
          { status: 400 },
        );
      }
    }

    // Remove the member
    const { error: removeError } = await supabase
      .from('user_organizations')
      .delete()
      .eq('user_id', userIdToRemove)
      .eq('organization_id', organizationId);

    if (removeError) {
      console.error('Failed to remove member:', removeError);
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Failed to remove member',
        },
        { status: 500 },
      );
    }

    return NextResponse.json<AuthResponse>({
      success: true,
      message: 'Member removed successfully',
    });
  } catch (error) {
    console.error('Remove member error:', error);
    return NextResponse.json<AuthResponse>(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}
