'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { SignupFormState, SignupFormData, AccountType } from '@/types/auth';
import { AccountTypeSelection } from './signup/AccountTypeSelection';
import { BasicInformation } from './signup/BasicInformation';
import { EmailVerification } from './signup/EmailVerification';
import { ProfileCompletion } from './signup/ProfileCompletion';

export function SignupFlow() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const invitationToken = searchParams.get('invitation');

  const [formState, setFormState] = useState<SignupFormState>({
    step: 1,
    data: {},
    loading: false,
    error: null,
    email_sent: false,
  });

  interface InvitationData {
    email: string;
    organization_name?: string;
    // Add other expected fields
  }

  const [invitationData, setInvitationData] = useState<InvitationData | null>(
    null,
  );

  // Load invitation data if token is present
  useEffect(() => {
    if (invitationToken) {
      loadInvitationData(invitationToken);
    }
  }, [invitationToken]);

  const loadInvitationData = async (token: string) => {
    try {
      const response = await fetch(`/api/invitations/${token}`);
      const result = await response.json();

      if (result.success) {
        setInvitationData(result.data);
        // Pre-fill form data for invitation signup
        setFormState((prev) => ({
          ...prev,
          data: {
            ...prev.data,
            email: result.data.email,
            account_type: 'company' as AccountType,
            organization_name: result.data.organization_name,
          },
        }));
      } else {
        setFormState((prev) => ({
          ...prev,
          error: result.error,
        }));
      }
    } catch (error) {
      console.error('Failed to load invitation:', error);
      setFormState((prev) => ({
        ...prev,
        error: 'Failed to load invitation details',
      }));
    }
  };

  const updateFormData = (data: Partial<SignupFormData>) => {
    setFormState((prev) => ({
      ...prev,
      data: { ...prev.data, ...data },
      error: null,
    }));
  };

  const nextStep = () => {
    setFormState((prev) => ({
      ...prev,
      step: Math.min(prev.step + 1, 4) as 1 | 2 | 3 | 4,
    }));
  };

  const prevStep = () => {
    setFormState((prev) => ({
      ...prev,
      step: Math.max(prev.step - 1, 1) as 1 | 2 | 3 | 4,
    }));
  };

  const setLoading = (loading: boolean) => {
    setFormState((prev) => ({ ...prev, loading }));
  };

  const setError = (error: string | null) => {
    setFormState((prev) => ({ ...prev, error }));
  };

  const setEmailSent = (sent: boolean) => {
    setFormState((prev) => ({ ...prev, email_sent: sent }));
  };

  const handleSignupComplete = async (formData: SignupFormData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          invitation_token: invitationToken,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setEmailSent(true);
        nextStep(); // Move to email verification step
      } else {
        setError(result.error || 'Signup failed');
      }
    } catch (error) {
      console.error('Signup error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailVerified = async (userId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/signup', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          invitation_token: invitationToken,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Redirect to dashboard or next step
        if (invitationToken) {
          router.push('/dashboard?welcome=true');
        } else {
          nextStep(); // Move to profile completion
        }
      } else {
        setError(result.error || 'Email verification failed');
      }
    } catch (error) {
      console.error('Email verification error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleProfileComplete = () => {
    router.push('/dashboard?welcome=true');
  };

  // Show error state if invitation is invalid
  if (invitationToken && formState.error && !invitationData) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Invalid Invitation
            </h2>
            <p className="mt-2 text-sm text-gray-600">{formState.error}</p>
            <button
              onClick={() => router.push('/auth/signup')}
              className="mt-4 flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
            >
              Sign up without invitation
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md space-y-8">
        {/* Progress indicator */}
        <div className="flex justify-center">
          <div className="flex space-x-2">
            {[1, 2, 3, 4].map((step) => (
              <div
                key={step}
                className={`h-3 w-3 rounded-full ${
                  step <= formState.step ? 'bg-indigo-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Step content */}
        {formState.step === 1 && (
          <AccountTypeSelection
            selectedType={formState.data.account_type}
            onSelect={(type) => updateFormData({ account_type: type })}
            onNext={nextStep}
            disabled={!!invitationToken} // Disable if invitation signup
            invitationData={invitationData}
          />
        )}

        {formState.step === 2 && (
          <BasicInformation
            formData={formState.data}
            onUpdate={updateFormData}
            onNext={handleSignupComplete}
            onBack={prevStep}
            loading={formState.loading}
            error={formState.error}
            invitationData={invitationData}
          />
        )}

        {formState.step === 3 &&
          (formState.data.email ? (
            <EmailVerification
              email={formState.data.email}
              onVerified={handleEmailVerified}
              onBack={prevStep}
              loading={formState.loading}
              error={formState.error}
            />
          ) : (
            <div className="text-center">
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                Email Required
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                Email is required to proceed with verification.
              </p>
              <button
                onClick={prevStep}
                className="mt-4 flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
              >
                Go Back
              </button>
            </div>
          ))}

        {formState.step === 4 && (
          <ProfileCompletion
            formData={formState.data}
            onUpdate={updateFormData}
            onComplete={handleProfileComplete}
            onBack={prevStep}
            loading={formState.loading}
            error={formState.error}
          />
        )}
      </div>
    </div>
  );
}
