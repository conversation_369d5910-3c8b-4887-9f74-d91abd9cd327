'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import {
  OrganizationRole,
  UserPermissions,
  ROLE_PERMISSIONS,
} from '@/types/auth';
import { InviteUserModal } from './InviteUserModal';
import { useTranslations } from 'next-intl';

interface TeamMember {
  id: string;
  role: OrganizationRole;
  joined_at: string;
  invited_by?: string;
  invited_at?: string;
  user_profile: {
    id: string;
    email: string;
    full_name: string;
    phone?: string;
    avatar_url?: string;
    created_at: string;
  };
  invited_by_profile?: {
    full_name: string;
    email: string;
  };
}

interface Invitation {
  id: string;
  email: string;
  role: OrganizationRole;
  status: string;
  invited_by_name: string;
  expires_at: string;
  created_at: string;
  token: string;
}

interface TeamManagementProps {
  organizationId: string;
  currentUserRole: OrganizationRole;
  currentUserId: string;
}

export function TeamManagement({
  organizationId,
  currentUserRole,
  currentUserId,
}: TeamManagementProps) {
  const t = useTranslations('Team');

  const [members, setMembers] = useState<TeamMember[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [inviteModalOpen, setInviteModalOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const currentUserPermissions: UserPermissions =
    ROLE_PERMISSIONS[currentUserRole];

  const loadTeamData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAuthToken();

      // Load team members
      const membersResponse = await fetch(
        `/api/organizations/${organizationId}/members`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (membersResponse.ok) {
        const membersResult = await membersResponse.json();
        setMembers(membersResult.data || []);
      }

      // Load pending invitations
      const invitationsResponse = await fetch(
        `/api/invitations?organization_id=${organizationId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (invitationsResponse.ok) {
        const invitationsResult = await invitationsResponse.json();
        setInvitations(invitationsResult.data || []);
      }
    } catch (error) {
      console.error('Failed to load team data:', error);
      setError(t('errors.loadFailed'));
    } finally {
      setLoading(false);
    }
  }, [organizationId, t]);

  useEffect(() => {
    loadTeamData();
  }, [loadTeamData]);

  const getAuthToken = async () => {
    // Get token from Supabase session
    const { supabase } = await import('@/lib/supabase');
    const {
      data: { session },
    } = await supabase.auth.getSession();
    return session?.access_token;
  };

  const handleInviteUser = async (email: string, role: OrganizationRole) => {
    try {
      const token = await getAuthToken();

      const response = await fetch('/api/invitations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          organization_id: organizationId,
          email,
          role,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Reload invitations
        await loadTeamData();
        // Show success message using a toast library
        // Example: toast.success(t('invite.success'));
        // Or use a state-based notification system
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Invite error:', error);
      // Example: toast.error(t('invite.error'));
      throw error;
    }
  };

  const handleUpdateRole = async (
    userId: string,
    newRole: OrganizationRole,
  ) => {
    setActionLoading(`role-${userId}`);

    try {
      const token = await getAuthToken();

      const response = await fetch(
        `/api/organizations/${organizationId}/members`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            user_id: userId,
            role: newRole,
          }),
        },
      );

      const result = await response.json();

      if (result.success) {
        // Update local state
        setMembers((prev) =>
          prev.map((member) =>
            member.user_profile.id === userId
              ? { ...member, role: newRole }
              : member,
          ),
        );
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Update role error:', error);
      alert(t('updateRole.error'));
    } finally {
      setActionLoading(null);
    }
  };

  const handleRemoveMember = async (userId: string) => {
    if (!confirm(t('removeMember.confirm'))) {
      return;
    }

    setActionLoading(`remove-${userId}`);

    try {
      const token = await getAuthToken();

      const response = await fetch(
        `/api/organizations/${organizationId}/members?user_id=${userId}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      const result = await response.json();

      if (result.success) {
        // Remove from local state
        setMembers((prev) =>
          prev.filter((member) => member.user_profile.id !== userId),
        );
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Remove member error:', error);
      alert(t('removeMember.error'));
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancelInvitation = async (
    invitationId: string,
    token: string,
  ) => {
    setActionLoading(`cancel-${invitationId}`);

    try {
      const authToken = await getAuthToken();

      const response = await fetch(`/api/invitations/${token}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });

      const result = await response.json();

      if (result.success) {
        // Remove from local state
        setInvitations((prev) => prev.filter((inv) => inv.id !== invitationId));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Cancel invitation error:', error);
      alert(t('cancelInvitation.error'));
    } finally {
      setActionLoading(null);
    }
  };

  const getRoleBadgeColor = (role: OrganizationRole) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      case 'member':
        return 'bg-green-100 text-green-800';
      case 'accountant':
        return 'bg-yellow-100 text-yellow-800';
      case 'viewer':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{t('title')}</h2>
          <p className="text-sm text-gray-600">{t('subtitle')}</p>
        </div>
        {currentUserPermissions.can_invite_users && (
          <button
            onClick={() => setInviteModalOpen(true)}
            className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
          >
            <svg
              className="mr-2 h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            {t('inviteUser')}
          </button>
        )}
      </div>

      {error && (
        <div className="rounded-md border border-red-200 bg-red-50 p-4">
          <p className="text-sm text-red-800">{error}</p>
        </div>
      )}

      {/* Team Members */}
      <div className="overflow-hidden bg-white shadow sm:rounded-md">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            {t('members.title')} ({members.length})
          </h3>
        </div>
        <ul className="divide-y divide-gray-200">
          {members.map((member) => (
            <li key={member.id} className="px-4 py-4 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="h-10 w-10 flex-shrink-0">
                    {member.user_profile.avatar_url ? (
                      <Image
                        className="h-10 w-10 rounded-full"
                        src={member.user_profile.avatar_url}
                        alt=""
                        width={40}
                        height={40}
                      />
                    ) : (
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-300">
                        <span className="text-sm font-medium text-gray-700">
                          {member.user_profile.full_name?.charAt(0) ||
                            member.user_profile.email.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="ml-4">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-gray-900">
                        {member.user_profile.full_name ||
                          member.user_profile.email}
                      </p>
                      {member.user_profile.id === currentUserId && (
                        <span className="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                          {t('members.you')}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500">
                      {member.user_profile.email}
                    </p>
                    <div className="mt-1 flex items-center">
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getRoleBadgeColor(member.role)}`}
                      >
                        {t(`roles.${member.role}.label`)}
                      </span>
                      <span className="ml-2 text-xs text-gray-500">
                        {t('members.joinedAt', {
                          date: new Date(member.joined_at).toLocaleDateString(),
                        })}
                      </span>
                    </div>
                  </div>
                </div>

                {currentUserPermissions.can_manage_users &&
                  member.user_profile.id !== currentUserId && (
                    <div className="flex items-center space-x-2">
                      <select
                        value={member.role}
                        onChange={(e) =>
                          handleUpdateRole(
                            member.user_profile.id,
                            e.target.value as OrganizationRole,
                          )
                        }
                        disabled={
                          actionLoading === `role-${member.user_profile.id}`
                        }
                        className="rounded-md border-gray-300 text-sm focus:border-indigo-500 focus:ring-indigo-500"
                      >
                        {Object.keys(ROLE_PERMISSIONS).map((role) => (
                          <option key={role} value={role}>
                            {t(`roles.${role}.label`)}
                          </option>
                        ))}
                      </select>
                      <button
                        onClick={() =>
                          handleRemoveMember(member.user_profile.id)
                        }
                        disabled={
                          actionLoading === `remove-${member.user_profile.id}`
                        }
                        className="text-sm text-red-600 hover:text-red-900"
                      >
                        {t('members.remove')}
                      </button>
                    </div>
                  )}
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* Pending Invitations */}
      {invitations.length > 0 && (
        <div className="overflow-hidden bg-white shadow sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              {t('invitations.title')} ({invitations.length})
            </h3>
          </div>
          <ul className="divide-y divide-gray-200">
            {invitations.map((invitation) => (
              <li key={invitation.id} className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {invitation.email}
                    </p>
                    <div className="mt-1 flex items-center">
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getRoleBadgeColor(invitation.role)}`}
                      >
                        {t(`roles.${invitation.role}.label`)}
                      </span>
                      <span className="ml-2 text-xs text-gray-500">
                        {t('invitations.invitedBy', {
                          name: invitation.invited_by_name,
                        })}
                      </span>
                      <span className="ml-2 text-xs text-gray-500">
                        {t('invitations.expiresAt', {
                          date: new Date(
                            invitation.expires_at,
                          ).toLocaleDateString(),
                        })}
                      </span>
                    </div>
                  </div>

                  {currentUserPermissions.can_manage_users && (
                    <button
                      onClick={() =>
                        handleCancelInvitation(invitation.id, invitation.token)
                      }
                      disabled={actionLoading === `cancel-${invitation.id}`}
                      className="text-sm text-red-600 hover:text-red-900"
                    >
                      {t('invitations.cancel')}
                    </button>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Invite User Modal */}
      <InviteUserModal
        isOpen={inviteModalOpen}
        onClose={() => setInviteModalOpen(false)}
        onInvite={handleInviteUser}
        organizationId={organizationId}
      />
    </div>
  );
}
