'use client';

import { AccountType } from '@/types/auth';
import { useTranslations } from 'next-intl';

interface AccountTypeSelectionProps {
  selectedType?: AccountType;
  onSelect: (type: AccountType) => void;
  onNext: () => void;
  disabled?: boolean;
  invitationData?: any;
}

export function AccountTypeSelection({
  selectedType,
  onSelect,
  onNext,
  disabled = false,
  invitationData,
}: AccountTypeSelectionProps) {
  const t = useTranslations('Auth.Signup');

  const handleNext = () => {
    if (selectedType) {
      onNext();
    }
  };

  // If this is an invitation signup, show invitation info
  if (invitationData) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            {t('invitation.title')}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {t('invitation.subtitle')}
          </p>
        </div>

        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-blue-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                {t('invitation.info.title')}
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  <strong>{t('invitation.info.organization')}:</strong>{' '}
                  {invitationData.organization_name}
                </p>
                <p>
                  <strong>{t('invitation.info.role')}:</strong>{' '}
                  {invitationData.role}
                </p>
                <p>
                  <strong>{t('invitation.info.invitedBy')}:</strong>{' '}
                  {invitationData.invited_by_name}
                </p>
              </div>
            </div>
          </div>
        </div>

        <button
          onClick={handleNext}
          className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
        >
          {t('invitation.continue')}
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          {t('accountType.title')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('accountType.subtitle')}
        </p>
      </div>

      <div className="space-y-4">
        {/* Freelancer Option */}
        <div
          className={`relative cursor-pointer rounded-lg border-2 p-6 transition-all ${
            selectedType === 'freelancer'
              ? 'border-indigo-600 bg-indigo-50'
              : 'border-gray-300 hover:border-gray-400'
          } ${disabled ? 'cursor-not-allowed opacity-50' : ''}`}
          onClick={() => !disabled && onSelect('freelancer')}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div
                className={`flex h-5 w-5 items-center justify-center rounded-full border-2 ${
                  selectedType === 'freelancer'
                    ? 'border-indigo-600 bg-indigo-600'
                    : 'border-gray-300'
                }`}
              >
                {selectedType === 'freelancer' && (
                  <div className="h-2 w-2 rounded-full bg-white" />
                )}
              </div>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-gray-900">
                {t('accountType.freelancer.title')}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {t('accountType.freelancer.description')}
              </p>
              <ul className="mt-2 space-y-1 text-sm text-gray-500">
                <li>• {t('accountType.freelancer.features.singleUser')}</li>
                <li>
                  • {t('accountType.freelancer.features.personalWorkspace')}
                </li>
                <li>• {t('accountType.freelancer.features.quickSetup')}</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Company Option */}
        <div
          className={`relative cursor-pointer rounded-lg border-2 p-6 transition-all ${
            selectedType === 'company'
              ? 'border-indigo-600 bg-indigo-50'
              : 'border-gray-300 hover:border-gray-400'
          } ${disabled ? 'cursor-not-allowed opacity-50' : ''}`}
          onClick={() => !disabled && onSelect('company')}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div
                className={`flex h-5 w-5 items-center justify-center rounded-full border-2 ${
                  selectedType === 'company'
                    ? 'border-indigo-600 bg-indigo-600'
                    : 'border-gray-300'
                }`}
              >
                {selectedType === 'company' && (
                  <div className="h-2 w-2 rounded-full bg-white" />
                )}
              </div>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-gray-900">
                {t('accountType.company.title')}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {t('accountType.company.description')}
              </p>
              <ul className="mt-2 space-y-1 text-sm text-gray-500">
                <li>• {t('accountType.company.features.multiUser')}</li>
                <li>• {t('accountType.company.features.roleBasedAccess')}</li>
                <li>• {t('accountType.company.features.teamCollaboration')}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <button
        onClick={handleNext}
        disabled={!selectedType || disabled}
        className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
      >
        {t('accountType.continue')}
      </button>
    </div>
  );
}
