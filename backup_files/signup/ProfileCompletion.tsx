'use client';

import { useState } from 'react';
import { SignupFormData } from '@/types/auth';
import { useTranslations } from 'next-intl';

interface ProfileCompletionProps {
  formData: Partial<SignupFormData>;
  onUpdate: (data: Partial<SignupFormData>) => void;
  onComplete: () => void;
  onBack: () => void;
  loading: boolean;
  error: string | null;
}

export function ProfileCompletion({
  formData,
  onUpdate,
  onComplete,
  onBack,
  loading,
  error,
}: ProfileCompletionProps) {
  const t = useTranslations('Auth.Signup');
  const [localData, setLocalData] = useState({
    phone: formData.phone || '',
    address: formData.address || '',
    ico: formData.ico || '',
    dic: formData.dic || '',
    is_vat_payer: formData.is_vat_payer || false,
    website: formData.website || '',
    bank_details: formData.bank_details || '',
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const handleInputChange = (field: string, value: string | boolean) => {
    setLocalData((prev) => ({ ...prev, [field]: value }));
    onUpdate({ [field]: value });

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // ICO validation (Czech business ID - 8 digits)
    if (localData.ico && !/^[0-9]{8}$/.test(localData.ico)) {
      errors.ico = t('profileCompletion.validation.icoInvalid');
    }

    // DIC validation (Czech VAT ID - format: **********)
    if (localData.dic && !/^[A-Z]{2}[0-9]{8,10}$/.test(localData.dic)) {
      errors.dic = t('profileCompletion.validation.dicInvalid');
    }

    // Website validation
    if (localData.website) {
      try {
        const url = new URL(localData.website);
        if (!['http:', 'https:'].includes(url.protocol)) {
          errors.website = t('profileCompletion.validation.websiteInvalid');
        }
      } catch {
        errors.website = t('profileCompletion.validation.websiteInvalid');
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // This step is optional, so we can proceed even with empty fields
    onComplete();
  };

  const handleSkip = () => {
    onComplete();
  };

  const isCompanyAccount = formData.account_type === 'company';

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
          <svg
            className="h-6 w-6 text-green-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          {t('profileCompletion.title')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('profileCompletion.subtitle')}
        </p>
      </div>

      {error && (
        <div className="rounded-md border border-red-200 bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Phone */}
        <div>
          <label
            htmlFor="phone"
            className="block text-sm font-medium text-gray-700"
          >
            {t('profileCompletion.fields.phone.label')}
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            autoComplete="tel"
            value={localData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className="relative mt-1 block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm"
            placeholder={t('profileCompletion.fields.phone.placeholder')}
          />
        </div>

        {/* Address */}
        <div>
          <label
            htmlFor="address"
            className="block text-sm font-medium text-gray-700"
          >
            {t('profileCompletion.fields.address.label')}
          </label>
          <textarea
            id="address"
            name="address"
            rows={3}
            value={localData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className="relative mt-1 block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm"
            placeholder={t('profileCompletion.fields.address.placeholder')}
          />
        </div>

        {/* Business fields for company accounts */}
        {isCompanyAccount && (
          <>
            <div className="border-t border-gray-200 pt-6">
              <h3 className="mb-4 text-lg font-medium text-gray-900">
                {t('profileCompletion.businessInfo.title')}
              </h3>
              <p className="mb-4 text-sm text-gray-600">
                {t('profileCompletion.businessInfo.subtitle')}
              </p>
            </div>

            {/* ICO (Czech Business ID) */}
            <div>
              <label
                htmlFor="ico"
                className="block text-sm font-medium text-gray-700"
              >
                {t('profileCompletion.fields.ico.label')}
              </label>
              <input
                id="ico"
                name="ico"
                type="text"
                maxLength={8}
                value={localData.ico}
                onChange={(e) =>
                  handleInputChange('ico', e.target.value.replace(/\D/g, ''))
                }
                className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
                  validationErrors.ico ? 'border-red-300' : 'border-gray-300'
                } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                placeholder={t('profileCompletion.fields.ico.placeholder')}
              />
              {validationErrors.ico && (
                <p className="mt-1 text-sm text-red-600">
                  {validationErrors.ico}
                </p>
              )}
            </div>

            {/* DIC (Czech VAT ID) */}
            <div>
              <label
                htmlFor="dic"
                className="block text-sm font-medium text-gray-700"
              >
                {t('profileCompletion.fields.dic.label')}
              </label>
              <input
                id="dic"
                name="dic"
                type="text"
                maxLength={12}
                value={localData.dic}
                onChange={(e) =>
                  handleInputChange('dic', e.target.value.toUpperCase())
                }
                className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
                  validationErrors.dic ? 'border-red-300' : 'border-gray-300'
                } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                placeholder={t('profileCompletion.fields.dic.placeholder')}
              />
              {validationErrors.dic && (
                <p className="mt-1 text-sm text-red-600">
                  {validationErrors.dic}
                </p>
              )}
            </div>

            {/* VAT Payer */}
            <div className="flex items-center">
              <input
                id="is_vat_payer"
                name="is_vat_payer"
                type="checkbox"
                checked={localData.is_vat_payer}
                onChange={(e) =>
                  handleInputChange('is_vat_payer', e.target.checked)
                }
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label
                htmlFor="is_vat_payer"
                className="ml-2 block text-sm text-gray-900"
              >
                {t('profileCompletion.fields.isVatPayer.label')}
              </label>
            </div>

            {/* Website */}
            <div>
              <label
                htmlFor="website"
                className="block text-sm font-medium text-gray-700"
              >
                {t('profileCompletion.fields.website.label')}
              </label>
              <input
                id="website"
                name="website"
                type="url"
                value={localData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
                  validationErrors.website
                    ? 'border-red-300'
                    : 'border-gray-300'
                } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                placeholder={t('profileCompletion.fields.website.placeholder')}
              />
              {validationErrors.website && (
                <p className="mt-1 text-sm text-red-600">
                  {validationErrors.website}
                </p>
              )}
            </div>

            {/* Bank Details */}
            <div>
              <label
                htmlFor="bank_details"
                className="block text-sm font-medium text-gray-700"
              >
                {t('profileCompletion.fields.bankDetails.label')}
              </label>
              <textarea
                id="bank_details"
                name="bank_details"
                rows={3}
                value={localData.bank_details}
                onChange={(e) =>
                  handleInputChange('bank_details', e.target.value)
                }
                className="relative mt-1 block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm"
                placeholder={t(
                  'profileCompletion.fields.bankDetails.placeholder',
                )}
              />
            </div>
          </>
        )}

        {/* Buttons */}
        <div className="flex space-x-4">
          <button
            type="button"
            onClick={handleSkip}
            disabled={loading}
            className="flex flex-1 justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          >
            {t('profileCompletion.skip')}
          </button>
          <button
            type="submit"
            disabled={loading}
            className="flex flex-1 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          >
            {loading
              ? t('profileCompletion.completing')
              : t('profileCompletion.complete')}
          </button>
        </div>
      </form>
    </div>
  );
}
