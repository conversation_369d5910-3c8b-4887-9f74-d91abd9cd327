'use client';

import { useState } from 'react';
import { SignupFormData } from '@/types/auth';
import { useTranslations } from 'next-intl';

interface BasicInformationProps {
  formData: Partial<SignupFormData>;
  onUpdate: (data: Partial<SignupFormData>) => void;
  onNext: (data: SignupFormData) => void;
  onBack: () => void;
  loading: boolean;
  error: string | null;
  invitationData?: any;
}

export function BasicInformation({
  formData,
  onUpdate,
  onNext,
  onBack,
  loading,
  error,
  invitationData,
}: BasicInformationProps) {
  const t = useTranslations('Auth.Signup');
  const [localData, setLocalData] = useState({
    email: formData.email || '',
    password: '',
    confirmPassword: '',
    full_name: formData.full_name || '',
    organization_name: formData.organization_name || '',
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const handleInputChange = (field: string, value: string) => {
    let processedValue = value;
    if (field === 'full_name' || field === 'organization_name') {
      processedValue = value.trim();
    }
    setLocalData((prev) => ({ ...prev, [field]: processedValue }));
    onUpdate({ [field]: processedValue });

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const handleInputBlur = (field: string, value: string) => {
    // Trim whitespace on blur for text fields that should not have leading/trailing spaces
    if (
      field === 'email' ||
      field === 'full_name' ||
      field === 'organization_name'
    ) {
      const trimmedValue = value.trim();
      if (trimmedValue !== value) {
        setLocalData((prev) => ({ ...prev, [field]: trimmedValue }));
        onUpdate({ [field]: trimmedValue });
      }
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // Account type validation
    if (!formData.account_type) {
      errors.account_type = t('basicInfo.validation.accountTypeRequired');
    } else if (
      formData.account_type !== 'freelancer' &&
      formData.account_type !== 'company'
    ) {
      errors.account_type = t('basicInfo.validation.accountTypeInvalid');
    }

    // Email validation
    if (!localData.email) {
      errors.email = t('basicInfo.validation.emailRequired');
    } else if (
      !/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(
        localData.email,
      )
    ) {
      errors.email = t('basicInfo.validation.emailInvalid');
    }

    // Password validation
    if (!localData.password) {
      errors.password = t('basicInfo.validation.passwordRequired');
    } else if (localData.password.length < 8) {
      errors.password = t('basicInfo.validation.passwordTooShort');
    }

    // Confirm password validation
    if (!localData.confirmPassword) {
      errors.confirmPassword = t(
        'basicInfo.validation.confirmPasswordRequired',
      );
    } else if (localData.password !== localData.confirmPassword) {
      errors.confirmPassword = t('basicInfo.validation.passwordMismatch');
    }

    // Full name validation
    if (!localData.full_name.trim()) {
      errors.full_name = t('basicInfo.validation.fullNameRequired');
    }

    // Organization name validation for company accounts
    if (
      formData.account_type === 'company' &&
      !localData.organization_name.trim()
    ) {
      errors.organization_name = t(
        'basicInfo.validation.organizationNameRequired',
      );
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Type guard to ensure account_type is defined
    if (!formData.account_type) {
      setValidationErrors((prev) => ({
        ...prev,
        account_type: t('basicInfo.validation.accountTypeRequired'),
      }));
      return;
    }

    // Validate account_type is one of the valid values
    if (
      formData.account_type !== 'freelancer' &&
      formData.account_type !== 'company'
    ) {
      setValidationErrors((prev) => ({
        ...prev,
        account_type: t('basicInfo.validation.accountTypeInvalid'),
      }));
      return;
    }

    // Ensure all text fields are trimmed before submission
    const submitData: SignupFormData = {
      account_type: formData.account_type,
      email: localData.email.trim(),
      password: localData.password, // Don't trim passwords as spaces might be intentional
      full_name: localData.full_name.trim(),
      organization_name:
        formData.account_type === 'company'
          ? localData.organization_name.trim()
          : undefined,
    };

    onNext(submitData);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          {invitationData
            ? t('basicInfo.titleInvitation')
            : t('basicInfo.title')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">{t('basicInfo.subtitle')}</p>
      </div>

      {error && (
        <div className="rounded-md border border-red-200 bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email */}
        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700"
          >
            {t('basicInfo.fields.email.label')}
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            disabled={!!invitationData} // Disable if invitation signup
            value={localData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            onBlur={(e) => handleInputBlur('email', e.target.value)}
            className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
              validationErrors.email ? 'border-red-300' : 'border-gray-300'
            } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm ${
              invitationData ? 'bg-gray-50' : ''
            }`}
            placeholder={t('basicInfo.fields.email.placeholder')}
          />
          {validationErrors.email && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.email}
            </p>
          )}
        </div>

        {/* Full Name */}
        <div>
          <label
            htmlFor="full_name"
            className="block text-sm font-medium text-gray-700"
          >
            {t('basicInfo.fields.fullName.label')}
          </label>
          <input
            id="full_name"
            name="full_name"
            type="text"
            autoComplete="name"
            required
            value={localData.full_name}
            onChange={(e) => handleInputChange('full_name', e.target.value)}
            onBlur={(e) => handleInputBlur('full_name', e.target.value)}
            className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
              validationErrors.full_name ? 'border-red-300' : 'border-gray-300'
            } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
            placeholder={t('basicInfo.fields.fullName.placeholder')}
          />
          {validationErrors.full_name && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.full_name}
            </p>
          )}
        </div>

        {/* Organization Name (for company accounts) */}
        {formData.account_type === 'company' && (
          <div>
            <label
              htmlFor="organization_name"
              className="block text-sm font-medium text-gray-700"
            >
              {t('basicInfo.fields.organizationName.label')}
            </label>
            <input
              id="organization_name"
              name="organization_name"
              type="text"
              required
              disabled={!!invitationData} // Disable if invitation signup
              value={localData.organization_name}
              onChange={(e) =>
                handleInputChange('organization_name', e.target.value)
              }
              onBlur={(e) =>
                handleInputBlur('organization_name', e.target.value)
              }
              className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
                validationErrors.organization_name
                  ? 'border-red-300'
                  : 'border-gray-300'
              } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm ${
                invitationData ? 'bg-gray-50' : ''
              }`}
              placeholder={t('basicInfo.fields.organizationName.placeholder')}
            />
            {validationErrors.organization_name && (
              <p className="mt-1 text-sm text-red-600">
                {validationErrors.organization_name}
              </p>
            )}
          </div>
        )}

        {/* Password */}
        <div>
          <label
            htmlFor="password"
            className="block text-sm font-medium text-gray-700"
          >
            {t('basicInfo.fields.password.label')}
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="new-password"
            required
            value={localData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
              validationErrors.password ? 'border-red-300' : 'border-gray-300'
            } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
            placeholder={t('basicInfo.fields.password.placeholder')}
          />
          {validationErrors.password && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.password}
            </p>
          )}
        </div>

        {/* Confirm Password */}
        <div>
          <label
            htmlFor="confirmPassword"
            className="block text-sm font-medium text-gray-700"
          >
            {t('basicInfo.fields.confirmPassword.label')}
          </label>
          <input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            autoComplete="new-password"
            required
            value={localData.confirmPassword}
            onChange={(e) =>
              handleInputChange('confirmPassword', e.target.value)
            }
            className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
              validationErrors.confirmPassword
                ? 'border-red-300'
                : 'border-gray-300'
            } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
            placeholder={t('basicInfo.fields.confirmPassword.placeholder')}
          />
          {validationErrors.confirmPassword && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.confirmPassword}
            </p>
          )}
        </div>

        {/* Buttons */}
        <div className="flex space-x-4">
          {!invitationData && (
            <button
              type="button"
              onClick={onBack}
              disabled={loading}
              className="flex flex-1 justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
            >
              {t('basicInfo.back')}
            </button>
          )}
          <button
            type="submit"
            disabled={loading}
            className={`${invitationData ? 'w-full' : 'flex-1'} flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50`}
          >
            {loading ? t('basicInfo.creating') : t('basicInfo.createAccount')}
          </button>
        </div>
      </form>
    </div>
  );
}
