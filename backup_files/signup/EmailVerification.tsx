'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { supabase } from '@/lib/supabase';

interface EmailVerificationProps {
  email: string;
  onVerified: (userId: string) => void;
  onBack: () => void;
  loading: boolean;
  error: string | null;
}

export function EmailVerification({
  email,
  onVerified,
  onBack,
  loading,
  error,
}: EmailVerificationProps) {
  const t = useTranslations('Auth.Signup');
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [checkingVerification, setCheckingVerification] = useState(false);

  // Start cooldown timer
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  // Listen for auth state changes (email verification)
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        // User has verified their email and is now signed in
        onVerified(session.user.id);
      }
    });

    return () => subscription.unsubscribe();
  }, [onVerified]);

  const handleResendEmail = async () => {
    setResendLoading(true);

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) {
        console.error('Resend error:', error);
      } else {
        setResendCooldown(60); // 60 second cooldown
      }
    } catch (error) {
      console.error('Resend error:', error);
    } finally {
      setResendLoading(false);
    }
  };

  const handleCheckVerification = async () => {
    setCheckingVerification(true);

    try {
      // First, try to get the current session
      const { data: sessionData } = await supabase.auth.getSession();

      if (sessionData.session?.user) {
        // User is already signed in, check if email is verified
        if (sessionData.session.user.email_confirmed_at) {
          onVerified(sessionData.session.user.id);
          return;
        }
      }

      // If no session exists, try to refresh to see if email verification created one
      const { data: refreshData, error: refreshError } =
        await supabase.auth.refreshSession();

      if (refreshError) {
        // If refresh fails due to no session, that's expected during signup
        // The user needs to click the verification link in their email first
        console.log(
          'No active session found. User needs to click the verification link in their email.',
        );

        // We could show a user-friendly message here, but for now we'll just log it
        // The auth state change listener will handle automatic sign-in when they click the email link
      } else if (
        refreshData.session?.user &&
        refreshData.session.user.email_confirmed_at
      ) {
        onVerified(refreshData.session.user.id);
      }
    } catch (error) {
      console.error('Check verification error:', error);
    } finally {
      setCheckingVerification(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
          <svg
            className="h-6 w-6 text-yellow-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 8l7.89 7.89a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
        </div>
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          {t('emailVerification.title')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('emailVerification.subtitle')}
        </p>
      </div>

      <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              {t('emailVerification.instructions.title')}
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>{t('emailVerification.instructions.step1')}</p>
              <p className="mt-1">
                <strong>{email}</strong>
              </p>
              <p className="mt-2">
                {t('emailVerification.instructions.step2')}
              </p>
              <p className="mt-1">
                {t('emailVerification.instructions.step3')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="rounded-md border border-red-200 bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {/* Check verification button */}
        <button
          onClick={handleCheckVerification}
          disabled={checkingVerification || loading}
          className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
        >
          {checkingVerification
            ? t('emailVerification.checking')
            : t('emailVerification.checkVerification')}
        </button>

        {/* Resend email button */}
        <button
          onClick={handleResendEmail}
          disabled={resendLoading || resendCooldown > 0 || loading}
          className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
        >
          {resendLoading
            ? t('emailVerification.resending')
            : resendCooldown > 0
              ? t('emailVerification.resendCooldown', {
                  seconds: resendCooldown,
                })
              : t('emailVerification.resendEmail')}
        </button>

        {/* Back button */}
        <button
          onClick={onBack}
          disabled={loading}
          className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
        >
          {t('emailVerification.back')}
        </button>
      </div>

      {/* Help text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          {t('emailVerification.help.checkSpam')}
        </p>
        <p className="mt-1 text-xs text-gray-500">
          {t('emailVerification.help.contactSupport')}
        </p>
      </div>
    </div>
  );
}
