'use client';

import { TeamManagement } from '@/components/team/TeamManagement';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/components/auth/ProtectedRoute';

// Force dynamic rendering for protected pages
export const dynamic = 'force-dynamic';

export default function TeamPage() {
  const { currentOrganization, currentRole, session, loading } = useAuth();

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div>Loading...</div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!currentOrganization || !currentRole || !session) {
    return (
      <ProtectedRoute>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div>Unable to load team data. Please try refreshing the page.</div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <TeamManagement
              organizationId={currentOrganization.id}
              currentUserRole={currentRole}
              currentUserId={session.user.id}
            />
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
