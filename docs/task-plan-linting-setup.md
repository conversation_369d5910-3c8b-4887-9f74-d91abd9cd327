# Task Plan: Implement Git Hooks and Linting (2025-06-25)

## Objective
Set up a comprehensive development workflow using `husky`, `commitlint`, `eslint`, and `prettier` to enforce code quality and consistent commit messages based on the Conventional Commits specification in `docs/commit.md`.

## Approach
This task will be executed in a series of isolated, verifiable steps:

1.  **Install Dependencies:** Add all required `devDependencies` for the new tooling (`husky`, `commitlint`, `lint-staged`, `prettier`, etc.).
2.  **Configure `commitlint`:** Create `commitlint.config.cjs` to enforce Conventional Commits.
3.  **Configure `prettier`:** Create `.prettierrc` to define code formatting rules.
4.  **Configure `lint-staged`:** Create `.lintstagedrc.js` to run `eslint` and `prettier` on staged files before they are committed.
5.  **Update `.eslintrc.json`:** Add `eslint-config-prettier` to prevent conflicts between ESLint and Prettier rules.
6.  **Initialize `husky`:** Run the setup script to create the `.husky` directory.
7.  **Create `husky` Hooks:**
    *   Add a `commit-msg` hook to validate commit messages with `commitlint`.
    *   Add a `pre-commit` hook to run `lint-staged`.
8.  **Update `package.json`:** Add a `prepare` script to enable `husky` upon installation.

## Files to be Touched
- **MODIFIED** `package.json` - To add dev dependencies and scripts.
- **MODIFIED** `.eslintrc.json` - To integrate Prettier.
- **NEW** `commitlint.config.cjs` - `commitlint` configuration.
- **NEW** `.prettierrc` - Prettier configuration.
- **NEW** `.lintstagedrc.js` - `lint-staged` configuration.
- **NEW** `.husky/commit-msg` - Git hook for commit message validation.
- **NEW** `.husky/pre-commit` - Git hook for pre-commit checks.

## Confirmation Checklist
- [ ] All dependencies are installed.
- [ ] `pre-commit` hook runs `eslint` and `prettier` on staged files.
- [ ] `commit-msg` hook prevents commits with non-conventional messages.
- [ ] The `prepare` script for `husky` is present in `package.json`.

---
Plan saved per Senior Engineer Execution Rule.
