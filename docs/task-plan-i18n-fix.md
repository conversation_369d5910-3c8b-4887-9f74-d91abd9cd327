# Task Plan: Fix Next.js 15 i18n 404 Error

**Objective:** Resolve the persistent 404 error for localized routes (e.g., `/en`) and ensure the landing page renders correctly with full internationalization support.

**Root Cause:** A cascade of fundamental misconfigurations across `next-intl` and Next.js 15 files, including syntax errors, incorrect logic, and missing required properties, introduced during previous failed attempts.

## The Definitive Plan

This plan follows the official `next-intl` documentation for the App Router and is the single source of truth for the fix.

### 1. Get Ground Truth
- Read the exact contents of the following files to ensure all subsequent edits are precise:
  - `src/i18n.ts`
  - `src/middleware.ts`
  - `src/app/[locale]/layout.tsx`
  - `next.config.mjs`

### 2. Implement Correct Configuration
- **`src/i18n.ts`**: Replace the entire file content to fix the critical syntax error and establish it as the single source of truth for loading messages.
- **`src/middleware.ts`**: Replace the entire file content to correctly import and use the configuration from `src/i18n.ts`, including the required `defaultLocale`.
- **`src/app/[locale]/layout.tsx`**: Add the required `setRequestLocale(locale)` function call to enable static rendering and ensure the framework correctly identifies the locale.
- **`next.config.mjs`**: Correct the path in the `createNextIntlPlugin` to point to `./src/i18n.ts`.

### 3. Clean Slate Installation & Verification
- Delete the `.next`, `node_modules`, and `package-lock.json` directories to remove any corrupted state.
- Run `npm install` to get fresh dependencies.
- Run `npm run type-check` to provide definitive proof that all type errors are resolved.

### 4. Final Type-Checking and Resolution
- The `type-check` command revealed two final errors:
  1. `Cannot find module 'next-intl/link'`: The import in `Header.tsx` was incorrect. The standard `next/link` should be used, as the middleware handles locale prefixing.
  2. `Property 'locale' is missing...`: The return object in `getRequestConfig` in `src/i18n.ts` was missing the required `locale` property.
- **Action**: Correct these two files.

### 5. Final Verification
- Run `npm run type-check` one last time to ensure a clean, error-free build.

### 6. Final Type Inference Correction
- The `type-check` command revealed one last error: the TypeScript compiler was incorrectly inferring the `locale` parameter as `string | undefined`.
- **Action**: Explicitly type the parameter of the `getRequestConfig` callback with `GetRequestConfigParams` to remove all type ambiguity.

### 7. Final Verification
- Run `npm run type-check` one last time to ensure a clean, error-free build.

### 8. Final Type Guard Correction
- The `type-check` command revealed one last, subtle error: the TypeScript compiler was still inferring the `locale` parameter as `string | undefined` because the type guard was not explicit enough.
- **Action**: Modify the validation logic in `src/i18n.ts` to use an explicit check (`if (!locale || !locales.includes(locale))`) to remove all type ambiguity.

### 9. Final Verification
- Run `npm run type-check` one last time to ensure a clean, error-free build.

### 10. Final Commit
- Once verified, commit all changes to the `feat/landing-page` branch with a clear, concise message.
