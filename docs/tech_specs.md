# Technical Specifications

_Last updated: 2025-06-25_

## Stack Overview

| Area            | Choice                                 |
| --------------- | -------------------------------------- |
| Framework       | Next.js 15 (App Router)                |
| Lang            | TypeScript                             |
| Styling         | Tailwind CSS                           |
| State / Data    | React Query v5                         |
| BaaS / DB       | Supabase                               |
| i18n            | next-intl (en, cz)                     |
| Notifications   | react-hot-toast                        |
| Testing         | Vitest (unit) + Playwright (e2e & API) |
| Lint / Format   | ESLint + Prettier                      |
| SEO             | next-seo                               |
| Sitemap         | next-sitemap                           |
| Release         | semantic-release                       |
| Package manager | npm                                    |

---

## Database – Supabase

The application relies on Supabase as its backend database. Configure the following environment variables (see [Supabase API keys documentation](https://supabase.com/docs/guides/api/api-keys)):

- `NEXT_PUBLIC_SUPABASE_URL` - Your Supabase project URL (client-side accessible)
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Your Supabase anon/public key (client-side accessible)
- `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key (server-side only, for admin operations)
- `NEXT_PUBLIC_APP_URL` - Your application URL (for invitation links and redirects)

### Database Schema

The database has been completely rebuilt with a comprehensive authentication system supporting both freelancer and company accounts:

#### Core Authentication Tables

- **`user_profiles`** - Extended user profiles with account type (freelancer/company)
- **`organizations`** - Organization/workspace management with Czech business compliance
- **`user_organizations`** - Multi-user organization relationships with role-based permissions

#### Business Logic Tables

- **`clients`** - Customer management with Czech tax compliance (ICO/DIC)
- **`products_services`** - Reusable product/service catalog
- **`invoices`** - Comprehensive invoice management with Czech requirements
- **`invoice_items`** - Invoice line items with automatic calculations

#### Key Features

- **Account Types**: Freelancer (single-user) vs Company (multi-user) workspaces
- **Role-Based Access**: Owner, Admin, Member, Accountant, Viewer roles
- **Czech Compliance**: ICO/DIC validation, VAT handling, Czech localization
- **Row Level Security**: Complete data isolation between organizations
- **Automatic Triggers**: Profile creation, invoice calculations, timestamp updates
- **Performance Indexes**: Optimized for common query patterns

#### Database Files

Database schema is managed through Supabase Dashboard or Supabase CLI migrations.
