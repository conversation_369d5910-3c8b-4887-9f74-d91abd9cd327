# Task Plan: Add next-intl configuration (2025-06-25)

## Objective
Resolve runtime error:
```
Couldn't find next-intl config file.
```
by providing the required configuration for `next-intl` in a **Next.js App Router** project.

## Approach
1. **Create `i18n.ts`** in the project root with:
   - `locales` array matching those already defined in `middleware.ts` (`['en', 'cz']`).
   - `defaultLocale` set to `'en'`.
   - Export the `Locale` helper type.
2. No existing files require modification; this is an additive, isolated change consistent with Windsurf rules (minimal, contained).
3. After implementation, start the dev server (`next dev`) to confirm the error is gone.

## Files to be Touched
- **NEW** `i18n.ts`  – configuration required by `next-intl`.

## Confirmation Checklist
- [ ] `i18n.ts` exists at the repository root.
- [ ] Build/runtime no longer throws "Couldn't find next-intl config file".

---
Plan saved per Senior Engineer Execution Rule.
