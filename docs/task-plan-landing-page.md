# Task Plan: Create New Landing Page (2025-06-25)

## Objective
Develop a new landing page for "Invoice Hub" with a look and feel inspired by the provided Cal.com design. The page will be built in a new branch, `feat/landing-page`.

## Approach
This task will be executed by creating a series of modular, reusable React components styled with Tailwind CSS.

1.  **Create UI Components:**
    *   `src/components/Header.tsx`: A navigation bar with the logo, links, and call-to-action buttons.
    *   `src/components/Hero.tsx`: The main hero section with the primary headline and sub-headline.
    *   `src/components/Features.tsx`: A section to display the three main product features in a card-based layout.

2.  **Update Homepage:** The main page at `src/app/[locale]/page.tsx` will be updated to assemble and render these new components.

3.  **Add Translations:** All text content for the new components will be added to the `en.json` and `cz.json` message files to support internationalization.

## Files to be Touched
- **NEW** `src/components/Header.tsx`
- **NEW** `src/components/Hero.tsx`
- **NEW** `src/components/Features.tsx`
- **MODIFIED** `src/app/[locale]/page.tsx` - To use the new components.
- **MODIFIED** `src/i18n/messages/en.json` - To add English translations.
- **MODIFIED** `src/i18n/messages/cz.json` - To add Czech translations.

## Confirmation Checklist
- [ ] All new components are created and styled.
- [ ] The homepage correctly renders the new landing page layout.
- [ ] All text is correctly translated and loaded from the message files.
- [ ] The new branch `feat/landing-page` contains all the changes.

---
Plan saved per Senior Engineer Execution Rule.
