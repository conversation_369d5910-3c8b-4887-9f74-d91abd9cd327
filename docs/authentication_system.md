# Authentication & User Management System

This document describes the comprehensive authentication and user management system implemented for the Invoice App, supporting both freelancer and company accounts with team collaboration features.

## 🏗️ System Architecture

### Account Types

#### 1. **Freelancer Profile**

- **Single-user workspace** for individual professionals
- **Cannot invite other users** to their organization
- **Automatic organization creation** during signup
- **Simplified onboarding** process
- **Perfect for**: Solo freelancers, consultants, small service providers

#### 2. **Company Account**

- **Multi-user organization** with team collaboration
- **Role-based permissions** system (5 distinct roles)
- **Team management** with user invitations
- **Shared workspace** for collaborative invoice management
- **Perfect for**: Small businesses, agencies, teams

### Role-Based Permissions

| Role           | Invite Users | Manage Users | Update Org | Delete Org | Create Invoices | Edit Invoices | Delete Invoices | View Financial |
| -------------- | ------------ | ------------ | ---------- | ---------- | --------------- | ------------- | --------------- | -------------- |
| **Owner**      | ✅           | ✅           | ✅         | ✅         | ✅              | ✅            | ✅              | ✅             |
| **Admin**      | ✅           | ✅           | ✅         | ❌         | ✅              | ✅            | ✅              | ✅             |
| **Member**     | ❌           | ❌           | ❌         | ❌         | ✅              | ✅            | ❌              | ✅             |
| **Accountant** | ❌           | ❌           | ❌         | ❌         | ❌              | ❌            | ❌              | ✅             |
| **Viewer**     | ❌           | ❌           | ❌         | ❌         | ❌              | ❌            | ❌              | ❌             |

## 🔐 Authentication Flows

### 1. Signup Flow (Multi-Step)

#### **Step 1: Account Type Selection**

- User chooses between Freelancer Profile or Company Account
- Different UI/UX based on selection
- Invitation users skip this step (pre-filled)

#### **Step 2: Basic Information**

- Email, password, full name
- Organization name (required for company accounts)
- Form validation with real-time feedback
- Disabled fields for invitation signups

#### **Step 3: Email Verification**

- Supabase Auth sends verification email
- Real-time verification status checking
- Resend email functionality with cooldown
- Auto-redirect on verification

#### **Step 4: Profile Completion (Optional)**

- Additional profile information
- Czech business details (ICO, DIC, VAT)
- Can be skipped and completed later

### 2. Login Flow

#### **Standard Login**

- Email/password authentication via Supabase Auth
- Session validation and profile loading
- Automatic redirect based on account type and onboarding status
- Edge case recovery for incomplete profiles

#### **Session Management**

- JWT tokens from Supabase Auth
- Local session storage for performance
- Automatic token refresh
- Secure logout with cleanup

### 3. Invitation Flow (Company Accounts Only)

#### **Sending Invitations**

- Only owners/admins can invite users
- Email validation and role assignment
- Secure token generation with expiration
- Email sending integration (ready for service)

#### **Accepting Invitations**

- Secure token validation
- Pre-filled signup form with organization details
- Automatic organization assignment
- Role-based access from day one

## 🛡️ Security Features

### Row Level Security (RLS)

- **Complete data isolation** between organizations
- **Role-based access control** within organizations
- **Automatic policy enforcement** at database level
- **Secure by default** - users can only access their own data

### Permission Validation

- **API-level permission checks** on all endpoints
- **Component-level access control** in UI
- **Database-level constraints** via RLS policies
- **Real-time permission updates** on role changes

### Edge Case Handling

- **Email verification recovery** for incomplete signups
- **Session restoration** after browser refresh
- **Profile recovery** for auth/profile mismatches
- **Invitation expiration** and cleanup
- **Rate limiting** on invitation sending

## 📁 File Structure

```
src/
├── types/auth.ts                     # Comprehensive TypeScript types
├── lib/supabase.ts                   # Supabase client with helpers
├── components/auth/
│   ├── SignupFlow.tsx               # Multi-step signup orchestrator
│   ├── LoginForm.tsx                # Login form with validation
│   ├── ProtectedRoute.tsx           # Route protection & auth hook
│   ├── AuthErrorBoundary.tsx        # Error handling & recovery
│   └── signup/
│       ├── AccountTypeSelection.tsx  # Step 1: Account type choice
│       ├── BasicInformation.tsx      # Step 2: User details
│       ├── EmailVerification.tsx     # Step 3: Email verification
│       └── ProfileCompletion.tsx     # Step 4: Additional details
├── components/team/
│   ├── TeamManagement.tsx           # Team overview & management
│   └── InviteUserModal.tsx          # User invitation modal
├── app/api/auth/
│   ├── signup/route.ts              # Signup API endpoint
│   └── login/route.ts               # Login API endpoint
├── app/api/invitations/
│   ├── route.ts                     # Invitation management
│   └── [token]/route.ts             # Token-specific operations
└── app/api/organizations/
    └── [id]/members/route.ts        # Team member management
```

## 🚀 Usage Examples

### Protected Routes

```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

// Basic protection (authenticated users only)
<ProtectedRoute>
  <DashboardContent />
</ProtectedRoute>

// Permission-based protection
<ProtectedRoute requiredPermissions={{ can_invite_users: true }}>
  <TeamManagement />
</ProtectedRoute>
```

### Auth Hook

```tsx
import { useAuth } from '@/components/auth/ProtectedRoute';

function MyComponent() {
  const {
    session,
    user,
    currentOrganization,
    currentRole,
    permissions,
    signOut,
  } = useAuth();

  if (permissions?.can_invite_users) {
    // Show invite button
  }
}
```

### Error Handling

```tsx
import { AuthErrorBoundary } from '@/components/auth/AuthErrorBoundary';

<AuthErrorBoundary>
  <AuthenticatedApp />
</AuthErrorBoundary>;
```

## 🔧 Configuration

### Environment Variables

```env
SUPABASE_URL=your_supabase_url
SUPABASE_PUBLISHABLE_KEY=your_supabase_publishable_key
SUPABASE_SECRET_KEY=your_supabase_secret_key
NEXT_PUBLIC_APP_URL=your_app_url
```

### Database Setup

Set up your database schema using the Supabase Dashboard SQL Editor or Supabase CLI migrations.

## 🌍 Internationalization

The system supports Czech/English localization with translation keys:

```json
{
  "Auth": {
    "Signup": {
      "accountType": {
        "title": "Choose Account Type",
        "freelancer": {
          "title": "Freelancer Profile",
          "description": "Individual workspace for personal invoice management"
        },
        "company": {
          "title": "Company Account",
          "description": "Team workspace with collaboration features"
        }
      }
    }
  }
}
```

## 🧪 Testing

### Test Scenarios

- [ ] Freelancer signup flow
- [ ] Company signup flow
- [ ] Invitation signup flow
- [ ] Email verification
- [ ] Login with various account states
- [ ] Permission-based access control
- [ ] Team member management
- [ ] Edge case recovery

### Security Testing

- [ ] RLS policy enforcement
- [ ] Permission validation
- [ ] Token security
- [ ] Session management
- [ ] Data isolation

## 🚀 Deployment Checklist

- [ ] Database schema applied
- [ ] Environment variables configured
- [ ] Email service integrated
- [ ] Error reporting setup
- [ ] Monitoring configured
- [ ] Security headers enabled
- [ ] Rate limiting implemented

## 🔮 Future Enhancements

- **SSO Integration** (Google, Microsoft, etc.)
- **Two-Factor Authentication** (2FA)
- **Advanced Role Management** (custom roles)
- **Audit Logging** (user actions tracking)
- **Organization Switching** (multi-org users)
- **API Key Management** (for integrations)

This authentication system provides a solid foundation for the Invoice App with enterprise-grade security, comprehensive user management, and excellent developer experience.
