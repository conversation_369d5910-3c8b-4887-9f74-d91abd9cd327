# Product Requirements Document (PRD)

**Product Name:** Invoice Hub
**Version:** 1.1 (Testing Strategy Revision)
**Date:** June 25, 2025
**Status:** Approved

---

### 1. Introduction & Strategic Vision

#### 1.1. Overview

This document outlines the product requirements for Invoice Hub, a web-based invoicing platform designed for the modern Czech and EU professional. Invoice Hub will prioritize speed, user experience, and automation, enabling users to manage their finances with minimal effort while ensuring full legal compliance.

#### 1.2. Problem Statement

Freelancers and small business owners in the Czech Republic are often burdened by administrative tasks. Existing invoicing solutions can be clunky, overpriced, or lack modern features like API integration and automated expense tracking. This administrative overhead steals time that could be spent on their core business. Accountants, in turn, often receive disorganized documents, making their work inefficient and error-prone.

#### 1.3. Product Vision

To become the most user-friendly and efficient financial administration tool for Czech and EU freelancers and small businesses by automating invoicing, expense tracking, and financial reporting.

#### 1.4. Unique Selling Proposition (USP)

Invoice Hub differentiates itself from competitors (like Fakturoid, iDoklad) by focusing on:

1.  **Extreme Simplicity:** A minimalist, opinionated interface that makes creating and sending a compliant invoice possible in under 60 seconds.
2.  **Automation First:** Built-in OCR for expense tracking and seamless ARES integration are standard, not premium, features.
3.  **Transparent Collaboration:** A revolutionary model for sharing real-time financial data with accountants, eliminating the end-of-month document scramble.

---

### 2. Target Audience & User Personas

- **Primary:**
  - **Jana, the Freelance Graphic Designer:** Tech-savvy but time-poor. Needs to create and send professional-looking invoices in both Czech and English quickly. She values design and a seamless user experience. Her biggest pain point is tracking small business expenses.
  - **Tomáš, the Small Business Owner (S.R.O.):** Manages a small e-commerce shop with 2 employees. Needs to manage clients, issue dozens of invoices per month, and keep an eye on cash flow. He is VAT-registered and needs to ensure all documents are compliant.

- **Secondary:**
  - **Pavel, the Accountant/Tax Manager:** Manages the books for 15 different small businesses, including Jana and Tomáš. His primary need is easy, read-only access to his clients' complete and accurate financial records (invoices and expenses) to prepare VAT returns and end-of-year taxes. He detests chasing clients for missing documents.

---

### 3. Strategic Plan & Phased Rollout

Development will proceed in a phased approach to deliver value incrementally and incorporate user feedback.

| Phase    | Name                              | Key Features & Goals                                                                                                                                                               | Success Metrics                                                                                     |
| :------- | :-------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------- |
| **MVP**  | **Core Invoicing Engine**         | • Secure User Auth & Single Company Setup<br>• Client & Product/Service CRUD<br>• Manual Invoice Creation & Management<br>• Compliant PDF Generation (Basic Template)              | • 50 Active Users in first month.<br>• >80% of new users successfully create an invoice.            |
| **V1.1** | **Efficiency & Automation**       | • ARES API Integration for client autofill<br>• Basic Dashboard (Total Invoiced, Outstanding)<br>• Secure, Shareable Invoice Links<br>• Invoice Status Updates (Draft, Sent, Paid) | • 30% reduction in average time to create a client.<br>• 50% of invoices are paid via shared links. |
| **V1.2** | **Collaboration & Expense**       | • Multi-user Management (Invite Admin/Accountant)<br>• Cost Registration with OCR<br>• Accountant's View (Read-only dashboard)                                                     | • 25% of companies invite at least one other user.<br>• 100 expenses scanned via OCR.               |
| **V1.3** | **Internationalization & Polish** | • Full UI localization (CZ/EN)<br>• Currency & Locale formatting for invoices<br>• Advanced Dashboard Analytics                                                                    | • 15% of invoices generated in English.<br>• Positive user feedback on dashboard usefulness.        |

---

### 4. Technical Architecture

#### 4.1. Technology Stack

- **Frontend:** Next.js (React) with TypeScript
- **Styling:** Tailwind CSS
- **State Management:** React Context / Zustand (for simplicity)
- **Backend Services:** Supabase (BaaS) & Next.js API Routes
- **Unit Testing:** Vitest, React Testing Library
- **E2E Testing:** Playwright

#### 4.2. Architectural Roles & Justification

This project will use a hybrid BaaS/Serverless architecture to maximize development speed and scalability.

- **Supabase (Primary Backend):**
  - **Database:** PostgreSQL. It will be the single source of truth for all data.
  - **Authentication:** Manages all user identity functions (registration, login, password reset, JWT handling).
  - **Storage:** Securely stores all user-generated files, specifically generated PDF invoices and uploaded expense receipts for OCR.

- **Next.js API Routes (Server Gateway):**
  - **Purpose:** To act as a secure intermediary for operations that require a trusted server environment or access to server-side resources.
  - **Use Cases:** ARES API Proxy, PDF Generation via Puppeteer.

---

### 5. Detailed Database Schema

The schema is designed for scalability and data integrity, using PostgreSQL features like ENUMs and JSONB.

| Table Name             | Column                   | Data Type                                               | Details                                              |
| :--------------------- | :----------------------- | :------------------------------------------------------ | :--------------------------------------------------- |
| **`Companies`**        | `id`                     | `uuid`                                                  | Primary Key, `default gen_random_uuid()`             |
|                        | `name`                   | `text`                                                  | Not Null                                             |
|                        | `address`                | `text`                                                  | Not Null                                             |
|                        | `ico`                    | `varchar(8)`                                            | IČO (Business ID). Unique.                           |
|                        | `dic`                    | `varchar(15)`                                           | DIČ (VAT ID), e.g., 'CZ12345678'. Can be Null.       |
|                        | `is_vat_payer`           | `boolean`                                               | `default false`                                      |
|                        | `bank_details`           | `text`                                                  | e.g., "Komerční Banka, *********/0100"               |
|                        | `created_at`             | `timestamptz`                                           | `default now()`                                      |
| **`Users`**            | `id`                     | `uuid`                                                  | PK, references `auth.users.id`                       |
|                        | `email`                  | `text`                                                  | Unique, Not Null                                     |
|                        | `full_name`              | `text`                                                  |                                                      |
| **`CompanyUsers`**     | `id`                     | `bigint`                                                | Primary Key                                          |
| (Join Table)           | `user_id`                | `uuid`                                                  | FK to `Users.id`, Not Null                           |
|                        | `company_id`             | `uuid`                                                  | FK to `Companies.id`, Not Null                       |
|                        | `role`                   | `enum('admin', 'user', 'accountant')`                   | Not Null                                             |
| **`Clients`**          | `id`                     | `uuid`                                                  | Primary Key                                          |
|                        | `company_id`             | `uuid`                                                  | FK to `Companies.id`                                 |
|                        | `name`                   | `text`                                                  | Not Null                                             |
|                        | `address`                | `text`                                                  |                                                      |
|                        | `ico`                    | `varchar(8)`                                            |                                                      |
|                        | `dic`                    | `varchar(15)`                                           |                                                      |
| **`ProductsServices`** | `id`                     | `uuid`                                                  | Primary Key                                          |
|                        | `company_id`             | `uuid`                                                  | FK to `Companies.id`                                 |
|                        | `description`            | `text`                                                  | Not Null                                             |
|                        | `unit_price`             | `numeric(10, 2)`                                        | Not Null                                             |
|                        | `unit`                   | `text`                                                  | e.g., "hour", "piece", "day". Default "piece".       |
|                        | `vat_rate`               | `numeric(4, 2)`                                         | e.g., 21.00, 15.00, 0.00. Not Null.                  |
| **`Invoices`**         | `id`                     | `uuid`                                                  | Primary Key                                          |
|                        | `company_id`             | `uuid`                                                  | FK to `Companies.id`                                 |
|                        | `client_id`              | `uuid`                                                  | FK to `Clients.id`                                   |
|                        | `invoice_number`         | `text`                                                  | Not Null. Must be unique per company.                |
|                        | `status`                 | `enum('draft', 'sent', 'paid', 'overdue', 'cancelled')` | Not Null, `default 'draft'`                          |
|                        | `date_of_issue`          | `date`                                                  | Not Null                                             |
|                        | `due_date`               | `date`                                                  | Not Null                                             |
|                        | `date_of_taxable_supply` | `date`                                                  | Not Null                                             |
|                        | `currency`               | `varchar(3)`                                            | Not Null, e.g., "CZK", "EUR"                         |
|                        | `total_amount`           | `numeric(12, 2)`                                        | Calculated sum                                       |
| **`InvoiceItems`**     | `id`                     | `bigint`                                                | Primary Key                                          |
|                        | `invoice_id`             | `uuid`                                                  | FK to `Invoices.id`                                  |
|                        | `description`            | `text`                                                  | **Copied** from Product/Service at time of creation. |
|                        | `quantity`               | `numeric(10, 2)`                                        | Not Null                                             |
|                        | `unit_price`             | `numeric(10, 2)`                                        | **Copied** from Product/Service.                     |
|                        | `vat_rate`               | `numeric(4, 2)`                                         | **Copied** from Product/Service.                     |
| **`Expenses`**         | `id`                     | `uuid`                                                  | Primary Key                                          |
|                        | `company_id`             | `uuid`                                                  | FK to `Companies.id`                                 |
|                        | `description`            | `text`                                                  |                                                      |
|                        | `amount`                 | `numeric(10, 2)`                                        | Not Null                                             |
|                        | `category`               | `text`                                                  | e.g., "Software", "Travel", "Office Supplies"        |
|                        | `date`                   | `date`                                                  | Not Null                                             |
|                        | `receipt_url`            | `text`                                                  | Link to receipt in Supabase Storage                  |
|                        | `ocr_data`               | `jsonb`                                                 | Stores structured data from OCR                      |

---

### 6. Feature Breakdown, User Stories & Acceptance Criteria

_(This section remains unchanged from the previous version)_

---

### 7. Compliance & Legal Requirements

_(This section remains unchanged from the previous version)_

---

### 8. UX & UI Standards

_(This section remains unchanged from the previous version)_

---

### 9. Test Strategy

Our testing strategy is built on two powerful pillars to ensure application quality, combining fast, isolated logic tests with comprehensive, real-world scenario tests.

#### Pillar 1: Unit Testing

- **Tools:** Vitest & React Testing Library.
- **Purpose:** To verify individual functions, React components, and custom hooks in isolation. These tests are fast, run frequently during development, and ensure the fundamental building blocks of our UI and business logic are correct.
- **Scope & Examples:**
  - **Logic:** Does a `calculateTotals(items)` function correctly sum prices and VAT?
  - **Component Rendering:** Does the `InvoiceStatusBadge` component correctly display the right color and text for a given status?
  - **Component Interaction:** When a user types in a search box within the `ClientSelector` component, does it correctly filter the list?

#### Pillar 2: End-to-End (E2E) & Integrated API Testing

- **Tool:** Playwright.
- **Purpose:** To simulate complete user journeys through the live application, validating both the frontend UI and the resulting backend state changes. This pillar serves as our primary **integration testing layer**, ensuring all parts of the system (Frontend, Next.js API, Supabase DB) work together as expected.
- **Methodology:** Tests will follow a "UI-Act, API-Verify" pattern.
  1.  **Act via UI:** A test will automate the browser to perform a series of actions as a real user would (e.g., log in, navigate to the invoices page, fill out the new invoice form, and click "Save").
  2.  **Verify via API:** After the UI action is complete, the test will use Playwright's built-in API request context (`page.request`) to make authenticated requests directly to our application's API endpoints. This provides immediate, robust, and non-brittle verification of the outcome.
- **Key Test Flows:**
  - **Full Invoice Lifecycle:**
    1.  (UI) Create a new client.
    2.  (UI) Create a new invoice for that client and save it as a draft.
    3.  **(API)** Make a `GET` request to `/api/invoices/[new_invoice_id]` and assert that the returned JSON data matches the data entered and the status is `'draft'`.
  - **Role-Based Access Control:**
    1.  (API) Programmatically create two users, one `Admin` and one `Accountant` for the same company.
    2.  (UI/API) Log in as the `Accountant`. Attempt to `POST` to the `/api/invoices` endpoint. Assert that the API returns a `403 Forbidden` status.
  - **ARES Integration:**
    1.  (UI) Navigate to the "Add Client" page, enter a valid IČO, and trigger the ARES fetch.
    2.  (UI) Assert that the name and address fields are populated.
    3.  (UI) Save the client.
    4.  **(API)** Make a `GET` request to `/api/clients/[new_client_id]` and verify that the data fetched from ARES has been correctly persisted to the database.

This two-pillar approach gives us the best of both worlds: rapid feedback on isolated logic from Vitest, and high confidence in the integrated system from our comprehensive Playwright test suite.

---

### 10. Out of Scope (For Post-V1.3)

To ensure focus, the following features are explicitly **out of scope** for the initial versions:

- Payment Gateway Integration (Stripe, PayPal).
- Recurring Invoices.
- Time Tracking Features.
- Advanced Inventory Management.
- Native Mobile Application (iOS/Android).
- Direct integration with Czech data mailboxes (`datová schránka`).
- **A dedicated, separate integration test suite.** Integration-level verification is embedded within our Playwright E2E tests, per the strategy outlined in Section 9.
