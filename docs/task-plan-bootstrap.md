# Task Plan – Project Bootstrap (Next.js + Supabase)

Date: 2025-06-25

## Objective
Bootstrap the base code for our Invoice App by cherry-picking features from the `nextbase-nextjs-supabase-starter` template, adjusted per your preferences.

## Final Stack (confirmed)
1. **Framework**: Next.js 15 (App Router)
2. **BaaS / DB**: Supabase
3. **Language**: TypeScript
4. **Styling**: Tailwind CSS
5. **State / Data**: React Query (TanStack Query) v5
6. **Notifications**: react-hot-toast
7. **Testing**:
   - Unit: Vitest 1.x + @testing-library/react
   - E2E & API: Playwright 1.x
8. **Lint / Format**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PostCSS
9. **SEO**: next-seo
10. **Sitemap**: next-sitemap
11. **Release automation**: semantic-release
12. **Package manager**: npm (no yarn/pnpm)
13. **i18n**: next-intl – initial locales: `en`, `cz` (adjustable)

## Work Breakdown

| Step | Description | Output |
|------|-------------|--------|
| 1 | Initialise npm workspace (`package.json`) and install latest package versions via Context7 lookup | `package.json`, `.nvmrc` |
| 2 | Configure Next.js 15 project skeleton (`src/app`, `next.config.js`) | baseline folders & config |
| 3 | Tailwind setup (`tailwind.config.ts`, `globals.css`) | styling ready |
| 4 | Supabase client init (`lib/supabase.ts`) + env example | supabase hook |
| 5 | React Query provider (`providers/QueryProvider.tsx`) | data fetching scaffold |
| 6 | i18n setup with next-intl (`middleware.ts`, `locales/`) | locale routing |
| 7 | Toast provider (`providers/ToastProvider.tsx`) | notifications |
| 8 | ESLint + Prettier configs | lint/format |
| 9 | next-seo & sitemap config | SEO |
|10 | Vitest + Playwright configs | tests |
|11 | semantic-release config | CI release |
|12 | Update `docs/tech_specs.md` with each completed step | docs |

## Notes / Assumptions
- Locales default to English (`en`) and Italian (`it`). Feel free to change.
- Node ≥ 20 is assumed; will pin in `.nvmrc`.
- Supabase URL & keys will live in `.env.local` (documented in tech_specs).

## Next Action
Begin **Step 1** – generate `package.json` with the chosen dependencies. Tech specs will be updated right after.
