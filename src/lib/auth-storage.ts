/**
 * Secure authentication storage utility
 *
 * This utility provides secure session management by:
 * 1. Using sessionStorage instead of localStorage (cleared on tab close)
 * 2. Encrypting sensitive data before storage
 * 3. Implementing session expiration
 * 4. Providing secure cleanup methods
 */

import { AuthSession } from '@/types/auth';

const SESSION_KEY = 'auth_session';
const SESSION_EXPIRY_KEY = 'auth_session_expiry';
const SESSION_DURATION = 8 * 60 * 60 * 1000; // 8 hours in milliseconds

/**
 * AES-GCM encryption/decryption using Web Crypto API
 * Key is generated per session and stored in sessionStorage
 */
const ENCRYPTION_KEY_NAME = 'auth_encryption_key';

async function getKey(): Promise<CryptoKey> {
  let keyData = sessionStorage.getItem(ENCRYPTION_KEY_NAME);
  if (keyData) {
    const raw = Uint8Array.from(atob(keyData), (c) => c.charCodeAt(0));
    return await window.crypto.subtle.importKey(
      'raw',
      raw,
      { name: 'AES-GCM' },
      false,
      ['encrypt', 'decrypt'],
    );
  } else {
    const key = await window.crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      true,
      ['encrypt', 'decrypt'],
    );
    const exported = await window.crypto.subtle.exportKey('raw', key);
    const b64 = btoa(String.fromCharCode(...new Uint8Array(exported)));
    sessionStorage.setItem(ENCRYPTION_KEY_NAME, b64);
    return key;
  }
}

export const encrypt = async (data: string): Promise<string> => {
  try {
    const key = await getKey();
    const iv = window.crypto.getRandomValues(new Uint8Array(12));
    const encoded = new TextEncoder().encode(data);
    const ciphertext = await window.crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      encoded,
    );
    // Store IV + ciphertext as base64
    const ivB64 = btoa(String.fromCharCode(...iv));
    const ctB64 = btoa(String.fromCharCode(...new Uint8Array(ciphertext)));
    return `${ivB64}:${ctB64}`;
  } catch (error) {
    console.error('Encryption failed:', error);
    return data;
  }
};

export const decrypt = async (encryptedData: string): Promise<string> => {
  try {
    const [ivB64, ctB64] = encryptedData.split(':');
    const iv = Uint8Array.from(atob(ivB64), (c) => c.charCodeAt(0));
    const ciphertext = Uint8Array.from(atob(ctB64), (c) => c.charCodeAt(0));
    const key = await getKey();
    const decrypted = await window.crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      key,
      ciphertext,
    );
    return new TextDecoder().decode(decrypted);
  } catch (error) {
    console.error('Decryption failed:', error);
    return encryptedData;
  }
};

/**
 * Check if session storage is available
 */
const isSessionStorageAvailable = (): boolean => {
  try {
    const test = '__sessionStorage_test__';
    sessionStorage.setItem(test, test);
    sessionStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
};

/**
 * Check if session has expired
 */
const isSessionExpired = (): boolean => {
  if (!isSessionStorageAvailable()) return true;

  const expiryTime = sessionStorage.getItem(SESSION_EXPIRY_KEY);
  if (!expiryTime) return true;

  return Date.now() > parseInt(expiryTime, 10);
};

/**
 * Store session data securely
 */
export const storeSession = async (session: AuthSession): Promise<boolean> => {
  if (!isSessionStorageAvailable()) {
    console.warn('SessionStorage not available, session will not persist');
    return false;
  }

  try {
    // Remove sensitive data that shouldn't be stored client-side
    const sanitizedSession = {
      ...session,
      // Keep only essential data, remove any tokens or sensitive info
      user: {
        id: session.user.id,
        email: session.user.email,
        email_confirmed_at: session.user.email_confirmed_at,
        created_at: session.user.created_at,
        updated_at: session.user.updated_at,
      },
    };

    const sessionData = JSON.stringify(sanitizedSession);
    const encryptedData = await encrypt(sessionData);
    const expiryTime = Date.now() + SESSION_DURATION;

    sessionStorage.setItem(SESSION_KEY, encryptedData);
    sessionStorage.setItem(SESSION_EXPIRY_KEY, expiryTime.toString());

    return true;
  } catch (error) {
    console.error('Failed to store session:', error);
    return false;
  }
};

/**
 * Retrieve session data securely
 */
export const getSession = async (): Promise<AuthSession | null> => {
  if (!isSessionStorageAvailable()) {
    return null;
  }

  if (isSessionExpired()) {
    clearSession();
    return null;
  }

  try {
    const encryptedData = sessionStorage.getItem(SESSION_KEY);
    if (!encryptedData) return null;

    const sessionData = await decrypt(encryptedData);
    return JSON.parse(sessionData) as AuthSession;
  } catch (error) {
    console.error('Failed to retrieve session:', error);
    clearSession();
    return null;
  }
};

/**
 * Clear session data
 */
export const clearSession = (): void => {
  if (!isSessionStorageAvailable()) return;

  try {
    sessionStorage.removeItem(SESSION_KEY);
    sessionStorage.removeItem(SESSION_EXPIRY_KEY);
  } catch (error) {
    console.error('Failed to clear session:', error);
  }
};

/**
 * Update session data
 */
export const updateSession = async (session: AuthSession): Promise<boolean> => {
  return storeSession(session);
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  return (await getSession()) !== null;
};

/**
 * Get session expiry time
 */
export const getSessionExpiry = (): number | null => {
  if (!isSessionStorageAvailable()) return null;

  const expiryTime = sessionStorage.getItem(SESSION_EXPIRY_KEY);
  return expiryTime ? parseInt(expiryTime, 10) : null;
};

/**
 * Extend session expiry
 */
export const extendSession = async (): Promise<boolean> => {
  if (!isSessionStorageAvailable() || !(await getSession())) return false;

  try {
    const newExpiryTime = Date.now() + SESSION_DURATION;
    sessionStorage.setItem(SESSION_EXPIRY_KEY, newExpiryTime.toString());
    return true;
  } catch (error) {
    console.error('Failed to extend session:', error);
    return false;
  }
};

/**
 * Session storage utility object
 */
export const authStorage = {
  store: storeSession,
  get: getSession,
  clear: clearSession,
  update: updateSession,
  isAuthenticated,
  getExpiry: getSessionExpiry,
  extend: extendSession,
  isExpired: isSessionExpired,
};

export default authStorage;
