import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { AuthResponse } from '@/types/auth';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SECRET_KEY!,
);

// Handle profile completion POST request
export async function POST(request: NextRequest) {
  try {
    // Get user from authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Authorization required',
        },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Invalid authorization',
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { phone, address, ico, dic, is_vat_payer, website, bank_details } = body;

    try {
      // Update user profile with additional information
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          phone: phone || null,
          address: address || null,
          ico: ico || null,
          dic: dic || null,
          is_vat_payer: is_vat_payer || false,
          website: website || null,
          bank_details: bank_details || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('Profile update error:', updateError);
        return NextResponse.json<AuthResponse>(
          {
            success: false,
            error: 'Failed to update profile',
          },
          { status: 500 }
        );
      }

      return NextResponse.json<AuthResponse>({
        success: true,
        message: 'Profile completed successfully',
      });

    } catch (error) {
      console.error('Profile completion error:', error);
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'An unexpected error occurred during profile completion',
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Request parsing error:', error);
    return NextResponse.json<AuthResponse>(
      {
        success: false,
        error: 'Invalid request format',
      },
      { status: 400 }
    );
  }
}
