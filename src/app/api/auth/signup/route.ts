import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { SignupFormData, AuthResponse } from '@/types/auth';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SECRET_KEY!,
);

// Handle signup POST request
export async function POST(request: NextRequest) {
  try {
    const body: SignupFormData & { invitation_token?: string } =
      await request.json();
    const {
      email,
      password,
      full_name,
      account_type,
      organization_name,
      invitation_token,
    } = body;

    // Validate required fields
    if (!email || !password || !full_name || !account_type) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Missing required fields',
        },
        { status: 400 },
      );
    }

    // Validate account type
    if (account_type !== 'freelancer' && account_type !== 'company') {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Invalid account type',
        },
        { status: 400 },
      );
    }

    // For company accounts, organization name is required
    if (account_type === 'company' && !organization_name) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'Organization name is required for company accounts',
        },
        { status: 400 },
      );
    }

    try {
      // Create user in Supabase Auth
      const { data: authData, error: authError } =
        await supabase.auth.admin.createUser({
          email,
          password,
          email_confirm: false, // Require email verification
          user_metadata: {
            full_name,
            account_type,
            organization_name:
              account_type === 'company' ? organization_name : undefined,
          },
        });

      if (authError) {
        console.error('Supabase auth error:', authError);
        return NextResponse.json<AuthResponse>(
          {
            success: false,
            error: authError.message || 'Failed to create user account',
          },
          { status: 400 },
        );
      }

      if (!authData.user) {
        return NextResponse.json<AuthResponse>(
          {
            success: false,
            error: 'Failed to create user account',
          },
          { status: 500 },
        );
      }

      // Send verification email
      const { error: emailError } = await supabase.auth.admin.generateLink({
        type: 'signup',
        email,
        password, // Required for signup type
      });

      if (emailError) {
        console.error('Email verification error:', emailError);
        // Don't fail the signup if email sending fails
      }

      return NextResponse.json<AuthResponse>({
        success: true,
        data: {
          user: authData.user,
          needs_verification: true,
        },
        message:
          'Account created successfully. Please check your email for verification.',
      });
    } catch (error) {
      console.error('Signup error:', error);
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'An unexpected error occurred during signup',
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Request parsing error:', error);
    return NextResponse.json<AuthResponse>(
      {
        success: false,
        error: 'Invalid request format',
      },
      { status: 400 },
    );
  }
}

// Handle email verification completion PATCH request
export async function PATCH(request: NextRequest) {
  try {
    const body: { user_id: string; invitation_token?: string } =
      await request.json();
    const { user_id, invitation_token } = body;

    if (!user_id) {
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'User ID is required',
        },
        { status: 400 },
      );
    }

    try {
      // Get user data from Supabase
      const { data: userData, error: userError } =
        await supabase.auth.admin.getUserById(user_id);

      if (userError || !userData.user) {
        return NextResponse.json<AuthResponse>(
          {
            success: false,
            error: 'User not found',
          },
          { status: 404 },
        );
      }

      const user = userData.user;
      const { full_name, account_type, organization_name } = user.user_metadata;

      // Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: user.id,
          email: user.email!,
          full_name,
          account_type,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (profileError) {
        console.error('Profile creation error:', profileError);
        return NextResponse.json<AuthResponse>(
          {
            success: false,
            error: 'Failed to create user profile',
          },
          { status: 500 },
        );
      }

      // If this is a company account, create organization
      if (account_type === 'company' && organization_name) {
        const { data: orgData, error: orgError } = await supabase
          .from('organizations')
          .insert({
            name: organization_name,
            created_by: user.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (orgError) {
          console.error('Organization creation error:', orgError);
          return NextResponse.json<AuthResponse>(
            {
              success: false,
              error: 'Failed to create organization',
            },
            { status: 500 },
          );
        }

        // Add user to organization as owner
        const { error: memberError } = await supabase
          .from('user_organizations')
          .insert({
            user_id: user.id,
            organization_id: orgData.id,
            role: 'owner',
            joined_at: new Date().toISOString(),
          });

        if (memberError) {
          console.error('Organization membership error:', memberError);
          return NextResponse.json<AuthResponse>(
            {
              success: false,
              error: 'Failed to add user to organization',
            },
            { status: 500 },
          );
        }
      }

      return NextResponse.json<AuthResponse>({
        success: true,
        message: 'Email verification completed successfully',
      });
    } catch (error) {
      console.error('Email verification completion error:', error);
      return NextResponse.json<AuthResponse>(
        {
          success: false,
          error: 'An unexpected error occurred during email verification',
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Request parsing error:', error);
    return NextResponse.json<AuthResponse>(
      {
        success: false,
        error: 'Invalid request format',
      },
      { status: 400 },
    );
  }
}
