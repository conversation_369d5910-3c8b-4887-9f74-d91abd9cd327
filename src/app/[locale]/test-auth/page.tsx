'use client';

import { useState } from 'react';
import { supabase } from '@/lib/supabase';

export default function TestAuthPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setResult('Testing Supabase connection...');

    try {
      // Test basic connection
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        setResult(`Connection Error: ${error.message}`);
      } else {
        setResult(`✅ Supabase connection successful!\nSession: ${data.session ? 'Active' : 'None'}`);
      }
    } catch (error) {
      setResult(`❌ Connection failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testSignup = async () => {
    setLoading(true);
    setResult('Testing signup...');

    try {
      const testEmail = `test-${Date.now()}@example.com`;
      const testPassword = 'testpassword123';

      const { data, error } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
      });

      if (error) {
        setResult(`❌ Signup Error: ${error.message}`);
      } else {
        setResult(`✅ Signup successful!\nUser ID: ${data.user?.id}\nEmail: ${data.user?.email}\nConfirmed: ${data.user?.email_confirmed_at ? 'Yes' : 'No'}`);
      }
    } catch (error) {
      setResult(`❌ Signup failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    setResult('Testing login with test credentials...');

    try {
      // Try to login with a test account
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword123',
      });

      if (error) {
        setResult(`❌ Login Error: ${error.message}`);
      } else {
        setResult(`✅ Login successful!\nUser ID: ${data.user?.id}\nEmail: ${data.user?.email}`);
      }
    } catch (error) {
      setResult(`❌ Login failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const checkConfig = () => {
    const url = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
    const key = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_KEY || process.env.SUPABASE_PUBLISHABLE_KEY;
    
    setResult(`Configuration Check:
URL: ${url ? '✅ Set' : '❌ Missing'}
Key: ${key ? '✅ Set' : '❌ Missing'}

URL Value: ${url || 'Not set'}
Key Value: ${key ? key.substring(0, 20) + '...' : 'Not set'}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Supabase Auth Test</h1>
        
        <div className="space-y-4 mb-8">
          <button
            onClick={checkConfig}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
          >
            Check Configuration
          </button>
          
          <button
            onClick={testConnection}
            disabled={loading}
            className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Connection'}
          </button>
          
          <button
            onClick={testSignup}
            disabled={loading}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Signup'}
          </button>
          
          <button
            onClick={testLogin}
            disabled={loading}
            className="w-full bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Login'}
          </button>
        </div>

        {result && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Result:</h2>
            <pre className="whitespace-pre-wrap text-sm text-gray-700">{result}</pre>
          </div>
        )}
      </div>
    </div>
  );
}
