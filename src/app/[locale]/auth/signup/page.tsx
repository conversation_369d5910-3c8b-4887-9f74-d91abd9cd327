import { SignupForm } from '@/components/auth/SignupForm';

type Props = {
  params: Promise<{
    locale: string;
  }>;
};

export default async function SignupPage(props: Props) {
  const params = await props.params;

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md space-y-8">
        <SignupForm />
      </div>
    </div>
  );
}
