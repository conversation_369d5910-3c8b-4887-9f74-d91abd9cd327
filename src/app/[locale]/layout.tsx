import '@/styles/globals.css';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages, setRequestLocale } from 'next-intl/server';
import { ReactNode } from 'react';
import { Toaster } from 'react-hot-toast';
import QueryProvider from '@/providers/QueryProvider';
import { locales, defaultLocale } from '@/i18n';

export const metadata = {
  title: 'Invoice Hub',
};

// Define the layout props type
type Props = {
  children: ReactNode;
  params: Promise<{
    locale: string;
  }>;
};

export default async function LocaleLayout(props: Props) {
  const params = await props.params;
  const locale = params.locale || defaultLocale;

  // Validate locale is supported
  if (!locales.includes(locale as any)) {
    return null; // Let the middleware handle the notFound redirect
  }

  // Set locale for the request to enable server-side internationalization
  setRequestLocale(locale);

  // Get messages for the current locale
  const messages = await getMessages();

  // Debug logging
  console.log('Layout - Locale:', locale);
  console.log('Layout - Messages keys:', Object.keys(messages || {}));
  console.log('Layout - Landing.Header.features:', messages?.Landing?.Header?.features);

  return (
    <html lang={locale} suppressHydrationWarning>
      <body>
        <NextIntlClientProvider messages={messages} locale={locale}>
          <QueryProvider>
            {props.children}
            <Toaster position="top-right" />
          </QueryProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
