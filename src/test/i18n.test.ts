import { describe, it, expect, beforeAll } from 'vitest';
import fs from 'fs';
import path from 'path';
import { routing } from '@/i18n/routing';

// Load translation files
const loadTranslations = (locale: string) => {
  const filePath = path.join(
    process.cwd(),
    'src/i18n/messages',
    `${locale}.json`,
  );
  return JSON.parse(fs.readFileSync(filePath, 'utf8'));
};

// Helper function to get all keys from a nested object
const getAllKeys = (obj: any, prefix = ''): string[] => {
  let keys: string[] = [];

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;

      if (
        typeof obj[key] === 'object' &&
        obj[key] !== null &&
        !Array.isArray(obj[key])
      ) {
        keys = keys.concat(getAllKeys(obj[key], fullKey));
      } else {
        keys.push(fullKey);
      }
    }
  }

  return keys;
};

// Helper function to check if a key exists in nested object
const hasKey = (obj: any, keyPath: string): boolean => {
  const keys = keyPath.split('.');
  let current = obj;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return false;
    }
  }

  return true;
};

describe('Internationalization (i18n)', () => {
  const translations: Record<string, any> = {};

  // Load all translation files
  beforeAll(() => {
    routing.locales.forEach((locale) => {
      translations[locale] = loadTranslations(locale);
    });
  });

  describe('Translation files structure', () => {
    it('should have translation files for all supported locales', () => {
      routing.locales.forEach((locale) => {
        expect(translations[locale]).toBeDefined();
        expect(typeof translations[locale]).toBe('object');
      });
    });

    it('should have consistent structure across all locales', () => {
      const [baseLocale, ...otherLocales] = routing.locales;
      const baseKeys = getAllKeys(translations[baseLocale]);

      otherLocales.forEach((locale) => {
        const localeKeys = getAllKeys(translations[locale]);

        // Check that all base keys exist in other locales
        baseKeys.forEach((key) => {
          expect(localeKeys).toContain(key);
        });

        // Check that no extra keys exist in other locales
        localeKeys.forEach((key) => {
          expect(baseKeys).toContain(key);
        });
      });
    });

    it('should not have empty translation values', () => {
      routing.locales.forEach((locale) => {
        const keys = getAllKeys(translations[locale]);

        keys.forEach((keyPath) => {
          const keys = keyPath.split('.');
          let value = translations[locale];

          for (const key of keys) {
            value = value[key];
          }

          expect(value).toBeTruthy();
          expect(typeof value).toBe('string');
          expect(value.trim()).not.toBe('');
        });
      });
    });
  });

  describe('Required translation keys', () => {
    const requiredKeys = [
      'LanguageSwitcher.switchLanguage',
      'Landing.Header.features',
      'Landing.Header.pricing',
      'Landing.Header.help',
      'Landing.Header.signIn',
      'Landing.Header.getStarted',
      'Landing.Header.toggleMenu',
      'Landing.Hero.title',
      'Landing.Hero.subtitle',
      'Auth.Errors.title',
      'Auth.Errors.unknownError',
      'Auth.Errors.invalidCredentials',
      'Auth.Errors.emailNotVerified',
      'Auth.Errors.userNotFound',
      'Auth.Errors.networkError',
      'Auth.Errors.sessionExpired',
      'Auth.Errors.retry',
      'Auth.Errors.reload',
      'Auth.Errors.goHome',
    ];

    it('should have all required translation keys in all locales', () => {
      routing.locales.forEach((locale) => {
        requiredKeys.forEach((key) => {
          expect(hasKey(translations[locale], key)).toBe(true);
        });
      });
    });
  });

  describe('Invoice-specific terminology', () => {
    const invoiceKeys = [
      // Add invoice-specific keys as they are implemented
    ];

    it('should have proper Czech invoice terminology', () => {
      const czechTranslations = translations['cs'];

      // Test some basic invoice terms that should be properly translated
      if (hasKey(czechTranslations, 'Invoice.title')) {
        const invoiceTitle = czechTranslations.Invoice?.title;
        expect(invoiceTitle).toMatch(/faktura|účet/i);
      }

      if (hasKey(czechTranslations, 'Invoice.amount')) {
        const amount = czechTranslations.Invoice?.amount;
        expect(amount).toMatch(/částka|suma|hodnota/i);
      }
    });
  });

  describe('Translation quality', () => {
    it('should not contain placeholder text or obvious untranslated content', () => {
      const placeholderPatterns = [
        /lorem ipsum/i,
        /placeholder/i,
        /todo/i,
        /fixme/i,
        /\[.*\]/,
        /{{.*}}/,
      ];

      routing.locales.forEach((locale) => {
        const keys = getAllKeys(translations[locale]);

        keys.forEach((keyPath) => {
          const keys = keyPath.split('.');
          let value = translations[locale];

          for (const key of keys) {
            value = value[key];
          }

          placeholderPatterns.forEach((pattern) => {
            expect(value).not.toMatch(pattern);
          });
        });
      });
    });

    it('should have proper Czech diacritics and characters', () => {
      // Load Czech translations directly if not available
      const czechTranslations = translations['cs'] || loadTranslations('cs');
      const keys = getAllKeys(czechTranslations);

      // Check that Czech translations contain Czech-specific characters
      const hasCzechChars = keys.some((keyPath) => {
        const keyParts = keyPath.split('.');
        let value = czechTranslations;

        for (const key of keyParts) {
          value = value[key];
        }

        if (typeof value !== 'string') {
          return false;
        }

        return /[áčďéěíňóřšťúůýž]/i.test(value);
      });

      expect(hasCzechChars).toBe(true);
    });
  });
});
