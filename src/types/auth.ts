// Authentication and User Management Types
// Comprehensive type definitions for the invoice app auth system

import { User as SupabaseUser } from '@supabase/supabase-js';

// Account Types
export type AccountType = 'freelancer' | 'company';

// Organization Roles
export type OrganizationRole =
  | 'owner'
  | 'admin'
  | 'member'
  | 'accountant'
  | 'viewer';

// Invitation Status
export type InvitationStatus = 'pending' | 'accepted' | 'expired' | 'cancelled';

// User Profile
export interface UserProfile {
  id: string;
  email: string;
  full_name: string | null;
  account_type: AccountType;
  phone: string | null;
  avatar_url: string | null;
  locale: string;
  timezone: string;
  onboarding_completed: boolean;
  created_at: string;
  updated_at: string;
}

// Organization
export interface Organization {
  id: string;
  name: string;
  slug: string | null;
  account_type: AccountType;
  address: string | null;
  ico: string | null; // Czech business ID
  dic: string | null; // Czech VAT ID
  is_vat_payer: boolean;
  bank_details: string | null;
  website: string | null;
  default_currency: string;
  default_locale: string;
  invoice_number_format: string;
  created_at: string;
  updated_at: string;
}

// User Organization Relationship
export interface UserOrganization {
  id: string;
  user_id: string;
  organization_id: string;
  role: OrganizationRole;
  invited_by: string | null;
  invited_at: string | null;
  joined_at: string;
  created_at: string;
  updated_at: string;
}

// Extended User with Organization Info
export interface UserWithOrganization extends UserProfile {
  organizations: Array<{
    organization: Organization;
    role: OrganizationRole;
    joined_at: string;
  }>;
  current_organization?: Organization;
  current_role?: OrganizationRole;
}

// Invitation
export interface Invitation {
  id: string;
  organization_id: string;
  email: string;
  role: OrganizationRole;
  invited_by: string;
  invited_by_name: string;
  organization_name: string;
  status: InvitationStatus;
  token: string;
  expires_at: string;
  accepted_at: string | null;
  created_at: string;
  updated_at: string;
}

// Signup Flow Types
export interface SignupStep1Data {
  account_type: AccountType;
}

export interface SignupStep2Data {
  email: string;
  password: string;
  full_name: string;
  organization_name?: string; // Required for company accounts
}

export interface SignupStep3Data {
  // Email verification step - no additional data needed
  verification_token?: string;
}

export interface SignupStep4Data {
  // Profile completion
  phone?: string;
  address?: string;
  ico?: string; // For Czech businesses
  dic?: string; // For Czech VAT
  is_vat_payer?: boolean;
  website?: string;
  bank_details?: string;
}

export interface SignupFormData
  extends SignupStep1Data,
    SignupStep2Data,
    SignupStep4Data {}

// Invitation Signup Data
export interface InvitationSignupData {
  invitation_token: string;
  email: string;
  password: string;
  full_name: string;
  phone?: string;
}

// Login Types
export interface LoginData {
  email: string;
  password: string;
}

export interface AuthSession {
  user: SupabaseUser;
  profile: UserProfile | null;
  organizations: UserWithOrganization['organizations'];
  current_organization: Organization | null;
  current_role: OrganizationRole | null;
}

// API Response Types
export interface AuthResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: any;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  };
}

export interface SignupResponse extends AuthResponse {
  data?: {
    user: SupabaseUser;
    needs_verification: boolean;
  };
}

export interface LoginResponse extends AuthResponse {
  data?: {
    session: AuthSession;
    redirect_url: string;
  };
}

export interface InvitationResponse extends AuthResponse {
  data?: {
    invitation: Invitation;
    sent: boolean;
  };
}

// Form State Types
export interface SignupFormState {
  step: 1 | 2 | 3 | 4;
  data: Partial<SignupFormData>;
  loading: boolean;
  error: string | null;
  email_sent: boolean;
}

export interface LoginFormState {
  loading: boolean;
  error: string | null;
}

export interface InvitationFormState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

// Permission Types
export interface UserPermissions {
  can_invite_users: boolean;
  can_manage_users: boolean;
  can_update_organization: boolean;
  can_delete_organization: boolean;
  can_create_invoices: boolean;
  can_edit_invoices: boolean;
  can_delete_invoices: boolean;
  can_view_financial_data: boolean;
  is_owner: boolean;
  is_admin: boolean;
}

// Role Permission Mapping
export const ROLE_PERMISSIONS: Record<OrganizationRole, UserPermissions> = {
  owner: {
    can_invite_users: true,
    can_manage_users: true,
    can_update_organization: true,
    can_delete_organization: true,
    can_create_invoices: true,
    can_edit_invoices: true,
    can_delete_invoices: true,
    can_view_financial_data: true,
    is_owner: true,
    is_admin: true,
  },
  admin: {
    can_invite_users: true,
    can_manage_users: true,
    can_update_organization: true,
    can_delete_organization: false,
    can_create_invoices: true,
    can_edit_invoices: true,
    can_delete_invoices: true,
    can_view_financial_data: true,
    is_owner: false,
    is_admin: true,
  },
  member: {
    can_invite_users: false,
    can_manage_users: false,
    can_update_organization: false,
    can_delete_organization: false,
    can_create_invoices: true,
    can_edit_invoices: true,
    can_delete_invoices: false,
    can_view_financial_data: true,
    is_owner: false,
    is_admin: false,
  },
  accountant: {
    can_invite_users: false,
    can_manage_users: false,
    can_update_organization: false,
    can_delete_organization: false,
    can_create_invoices: false,
    can_edit_invoices: false,
    can_delete_invoices: false,
    can_view_financial_data: true,
    is_owner: false,
    is_admin: false,
  },
  viewer: {
    can_invite_users: false,
    can_manage_users: false,
    can_update_organization: false,
    can_delete_organization: false,
    can_create_invoices: false,
    can_edit_invoices: false,
    can_delete_invoices: false,
    can_view_financial_data: false,
    is_owner: false,
    is_admin: false,
  },
};

// Utility Types
export type AuthError =
  | 'invalid_credentials'
  | 'email_not_verified'
  | 'user_not_found'
  | 'organization_not_found'
  | 'insufficient_permissions'
  | 'invitation_expired'
  | 'invitation_not_found'
  | 'email_already_exists'
  | 'weak_password'
  | 'network_error'
  | 'unknown_error';

export interface AuthErrorDetails {
  code: AuthError;
  message: string;
  details?: any;
}
