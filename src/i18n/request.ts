import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';
import { locales, defaultLocale, type Locale } from './index';

// Type guard function to check if a string is a valid locale
function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

export default getRequestConfig(async ({ locale }) => {
  // Ensure locale is never undefined and is valid
  const resolvedLocale = locale || defaultLocale;

  // Validate the locale parameter using type guard
  if (!isValidLocale(resolvedLocale)) {
    notFound();
  }

  // Load messages for the current locale
  try {
    const messages = (await import(`./messages/${resolvedLocale}.json`))
      .default;

    // Return with properly typed locale (never undefined)
    return {
      locale: resolvedLocale,
      messages,
    };
  } catch (error) {
    notFound();
  }
});
