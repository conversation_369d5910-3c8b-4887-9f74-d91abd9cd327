import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';
import { hasLocale } from 'next-intl';
import { routing } from './routing';
import { headers } from 'next/headers';

function extractLocaleFromUrl(url: string): string | null {
  if (!url) return null;

  try {
    const urlObj = new URL(
      url.startsWith('http') ? url : `http://localhost:3000${url}`,
    );
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    const potentialLocale = pathSegments[0];

    if (potentialLocale && hasLocale(routing.locales, potentialLocale)) {
      return potentialLocale;
    }
  } catch (e) {
    // Ignore URL parsing errors
  }

  return null;
}

export default getRequestConfig(async ({ requestLocale }) => {
  // Use requestLocale directly - it should be reliable when middleware is working correctly
  const locale = (await requestLocale) || routing.defaultLocale;

  // Ensure we have a valid locale
  const validLocale = hasLocale(routing.locales, locale)
    ? locale
    : routing.defaultLocale;

  console.log('getRequestConfig - requestLocale:', await requestLocale);
  console.log('getRequestConfig - final locale:', validLocale);

  // Load messages for the determined locale
  try {
    const messages = (await import(`./messages/${validLocale}.json`)).default;

    console.log(
      'getRequestConfig - sample translation:',
      messages?.Landing?.Header?.features,
    );

    return {
      locale: validLocale,
      messages,
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${validLocale}`, error);
    notFound();
  }
});
