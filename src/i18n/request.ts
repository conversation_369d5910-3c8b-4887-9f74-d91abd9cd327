import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';
import { hasLocale } from 'next-intl';
import { routing } from './routing';
import { headers } from 'next/headers';

function extractLocaleFromUrl(url: string): string | null {
  if (!url) return null;

  try {
    const urlObj = new URL(
      url.startsWith('http') ? url : `http://localhost:3000${url}`,
    );
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    const potentialLocale = pathSegments[0];

    if (potentialLocale && hasLocale(routing.locales, potentialLocale)) {
      return potentialLocale;
    }
  } catch (e) {
    // Ignore URL parsing errors
  }

  return null;
}

export default getRequestConfig(async ({ requestLocale }) => {
  const headersList = await headers();
  let locale: string | null = null;

  // Priority 1: Use requestLocale if available (this should work for URL segments)
  const requestedLocale = await requestLocale;
  if (requestedLocale && hasLocale(routing.locales, requestedLocale)) {
    locale = requestedLocale;
    console.log('getRequestConfig - using requestLocale:', locale);
  }

  // Priority 2: Extract locale from URL headers as fallback
  if (!locale) {
    const urlSources = [
      headersList.get('referer'),
      headersList.get('x-url'),
      headersList.get('x-pathname'),
      headersList.get('x-forwarded-url'),
      headersList.get('x-forwarded-host')
        ? `http://${headersList.get('x-forwarded-host')}${headersList.get('x-pathname') || ''}`
        : null,
    ];

    for (const url of urlSources) {
      if (url) {
        locale = extractLocaleFromUrl(url);
        if (locale) {
          console.log(
            'getRequestConfig - detected locale from URL:',
            locale,
            'source:',
            url,
          );
          break;
        }
      }
    }
  }

  // Priority 3: Try to extract from the current request URL using Next.js headers
  if (!locale) {
    const host = headersList.get('host');
    const pathname =
      headersList.get('x-pathname') || headersList.get('x-invoke-path');

    if (host && pathname) {
      const fullUrl = `http://${host}${pathname}`;
      locale = extractLocaleFromUrl(fullUrl);
      if (locale) {
        console.log(
          'getRequestConfig - detected locale from constructed URL:',
          locale,
        );
      }
    }
  }

  // Priority 4: Fallback to default locale
  if (!locale) {
    locale = routing.defaultLocale;
    console.log('getRequestConfig - fallback to default locale:', locale);
  }

  // Load messages for the determined locale
  try {
    const messages = (await import(`./messages/${locale}.json`)).default;

    console.log('getRequestConfig - final locale:', locale);
    console.log(
      'getRequestConfig - sample translation:',
      messages?.Landing?.Header?.features,
    );

    return {
      locale,
      messages,
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);
    notFound();
  }
});
