import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';
import { hasLocale } from 'next-intl';
import { routing } from './routing';
import { headers } from 'next/headers';

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  let locale = await requestLocale;

  console.log('getRequestConfig - requested locale:', locale);
  console.log('getRequestConfig - routing.locales:', routing.locales);

  // Check for NEXT_LOCALE cookie that might be overriding our locale
  const headersList = await headers();
  const cookieHeader = headersList.get('cookie');
  console.log('getRequestConfig - cookie header:', cookieHeader);

  // Extract locale from URL first to check if it conflicts with NEXT_LOCALE cookie
  let urlLocale = null;
  const refererUrl = headersList.get('referer');
  if (refererUrl) {
    try {
      const urlObj = new URL(refererUrl);
      const pathSegments = urlObj.pathname.split('/').filter(Boolean);
      const potentialLocale = pathSegments[0];
      if (potentialLocale && hasLocale(routing.locales, potentialLocale)) {
        urlLocale = potentialLocale;
        console.log('getRequestConfig - detected URL locale:', urlLocale);
      }
    } catch (e) {
      // Ignore URL parsing errors
    }
  }

  // If requestLocale is undefined, extract locale from URL
  if (!locale) {
    // Try different header names that might contain the URL
    // Prioritize referer as it contains the full URL
    const url =
      headersList.get('referer') ||
      headersList.get('x-url') ||
      headersList.get('x-pathname') ||
      headersList.get('x-forwarded-url') ||
      '';

    console.log('getRequestConfig - url from headers:', url);

    // Extract locale from URL path like /cs or /en
    let pathname = '';
    if (url) {
      try {
        const urlObj = new URL(
          url.startsWith('http') ? url : `http://localhost:3000${url}`,
        );
        pathname = urlObj.pathname;
      } catch (e) {
        // If URL parsing fails, treat url as pathname
        pathname = url.startsWith('/') ? url : `/${url}`;
      }
    }

    const pathSegments = pathname.split('/').filter(Boolean);
    const potentialLocale = pathSegments[0];

    console.log('getRequestConfig - pathname:', pathname);
    console.log(
      'getRequestConfig - potential locale from path:',
      potentialLocale,
    );

    if (potentialLocale && hasLocale(routing.locales, potentialLocale)) {
      locale = potentialLocale;
      console.log('getRequestConfig - extracted locale from URL:', locale);
    }
  }

  // Ensure that the incoming locale is valid
  if (!locale || !hasLocale(routing.locales, locale)) {
    locale = routing.defaultLocale;
  }

  console.log('getRequestConfig - final locale:', locale);

  // Load messages for the current locale
  try {
    const messages = (await import(`./messages/${locale}.json`)).default;

    console.log('getRequestConfig - loaded messages for locale:', locale);
    console.log(
      'getRequestConfig - message keys:',
      Object.keys(messages || {}),
    );
    console.log(
      'getRequestConfig - Landing.Header.features:',
      messages?.Landing?.Header?.features,
    );

    // Return with properly typed locale (never undefined)
    return {
      locale,
      messages,
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);
    notFound();
  }
});
