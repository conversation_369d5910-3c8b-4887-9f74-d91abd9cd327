import { getRequestConfig } from 'next-intl/server';
import { notFound } from 'next/navigation';

// Export the locales and defaultLocale for reuse
export const locales = ['en', 'cs'] as const;
export type Locale = (typeof locales)[number];
export const defaultLocale: Locale = 'en';

// Type-safe configuration for next-intl
export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming locale parameter is valid
  if (!locale || !locales.includes(locale as Locale)) {
    notFound();
  }

  return {
    locale: locale,
    messages: (await import(`./messages/${locale}.json`)).default,
  };
});
