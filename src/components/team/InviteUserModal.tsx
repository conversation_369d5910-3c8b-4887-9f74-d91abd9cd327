'use client';

import { useState } from 'react';
import { OrganizationRole } from '@/types/auth';
import { useTranslations } from 'next-intl';

interface InviteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInvite: (email: string, role: OrganizationRole) => Promise<void>;
  organizationId: string;
  loading?: boolean;
}

export function InviteUserModal({
  isOpen,
  onClose,
  onInvite,
  organizationId,
  loading = false,
}: InviteUserModalProps) {
  const t = useTranslations('Team.InviteUser');

  const [formData, setFormData] = useState({
    email: '',
    role: 'member' as OrganizationRole,
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const roles: {
    value: OrganizationRole;
    label: string;
    description: string;
  }[] = [
    {
      value: 'owner',
      label: t('roles.owner.label'),
      description: t('roles.owner.description'),
    },
    {
      value: 'admin',
      label: t('roles.admin.label'),
      description: t('roles.admin.description'),
    },
    {
      value: 'member',
      label: t('roles.member.label'),
      description: t('roles.member.description'),
    },
    {
      value: 'accountant',
      label: t('roles.accountant.label'),
      description: t('roles.accountant.description'),
    },
    {
      value: 'viewer',
      label: t('roles.viewer.label'),
      description: t('roles.viewer.description'),
    },
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // Email validation
    if (!formData.email) {
      errors.email = t('validation.emailRequired');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = t('validation.emailInvalid');
    }

    // Role validation
    if (!formData.role) {
      errors.role = t('validation.roleRequired');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await onInvite(formData.email, formData.role);

      // Reset form and close modal on success
      setFormData({ email: '', role: 'member' });
      setValidationErrors({});
      onClose();
    } catch (error) {
      // Error handling is done in parent component
      console.error('Invite error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting && !loading) {
      setFormData({ email: '', role: 'member' });
      setValidationErrors({});
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto"
      role="dialog"
      aria-modal="true"
      aria-labelledby="invite-modal-title"
      onKeyDown={(e) => {
        if (e.key === 'Escape' && !isSubmitting && !loading) {
          handleClose();
        }
      }}
    >
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="bg-opacity-75 fixed inset-0 bg-gray-500 transition-opacity"
          onClick={handleClose}
        />

        {/* Modal panel */}
        <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
          <h3
            id="invite-modal-title"
            className="text-lg leading-6 font-medium text-gray-900"
          >
            {/* Modal Title */}
          </h3>
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
                  <svg
                    className="h-6 w-6 text-indigo-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <div className="mt-3 w-full text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {t('title')}
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">{t('subtitle')}</p>
                  </div>

                  <div className="mt-6 space-y-4">
                    {/* Email Input */}
                    <div>
                      <label
                        htmlFor="invite-email"
                        className="block text-sm font-medium text-gray-700"
                      >
                        {t('fields.email.label')}
                      </label>
                      <input
                        type="email"
                        id="invite-email"
                        value={formData.email}
                        onChange={(e) =>
                          handleInputChange('email', e.target.value)
                        }
                        className={`mt-1 block w-full border px-3 py-2 ${
                          validationErrors.email
                            ? 'border-red-300'
                            : 'border-gray-300'
                        } rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                        placeholder={t('fields.email.placeholder')}
                        disabled={isSubmitting || loading}
                      />
                      {validationErrors.email && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.email}
                        </p>
                      )}
                    </div>

                    {/* Role Selection */}
                    <div>
                      <label
                        htmlFor="invite-role"
                        className="block text-sm font-medium text-gray-700"
                      >
                        {t('fields.role.label')}
                      </label>
                      <select
                        id="invite-role"
                        value={formData.role}
                        onChange={(e) =>
                          handleInputChange('role', e.target.value)
                        }
                        className={`mt-1 block w-full border px-3 py-2 ${
                          validationErrors.role
                            ? 'border-red-300'
                            : 'border-gray-300'
                        } rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                        disabled={isSubmitting || loading}
                      >
                        {roles.map((role) => (
                          <option key={role.value} value={role.value}>
                            {role.label}
                          </option>
                        ))}
                      </select>
                      {validationErrors.role && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.role}
                        </p>
                      )}
                    </div>

                    {/* Role Description */}
                    {formData.role && (
                      <div className="rounded-md bg-gray-50 p-3">
                        <p className="text-sm text-gray-600">
                          {
                            roles.find((r) => r.value === formData.role)
                              ?.description
                          }
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Actions */}
            <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
              <button
                type="submit"
                disabled={isSubmitting || loading}
                className="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 sm:ml-3 sm:w-auto sm:text-sm"
              >
                {isSubmitting ? t('sending') : t('sendInvitation')}
              </button>
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting || loading}
                className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                {t('cancel')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
