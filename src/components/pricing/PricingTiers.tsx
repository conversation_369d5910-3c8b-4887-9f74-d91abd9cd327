'use client';

import Link from 'next/link';
import { useState } from 'react';

const PricingCard = ({
  name,
  price,
  period,
  description,
  features,
  cta,
  ctaLink,
  popular = false,
  free = false,
}: {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  cta: string;
  ctaLink: string;
  popular?: boolean;
  free?: boolean;
}) => (
  <div
    className={`relative flex h-full flex-col rounded-2xl border p-8 ${
      popular
        ? 'scale-105 border-blue-500 bg-blue-50 shadow-xl'
        : 'border-gray-200 bg-white shadow-sm'
    }`}
  >
    {popular && (
      <div className="absolute -top-4 left-1/2 -translate-x-1/2">
        <span className="rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-1 text-sm font-medium text-white">
          Most Popular
        </span>
      </div>
    )}

    <div className="mb-8">
      <h3
        className={`text-xl font-semibold ${popular ? 'text-blue-900' : 'text-gray-900'}`}
      >
        {name}
      </h3>
      <p
        className={`mt-2 text-sm ${popular ? 'text-blue-700' : 'text-gray-600'}`}
      >
        {description}
      </p>
    </div>

    <div className="mb-8">
      <div className="flex items-baseline">
        <span
          className={`text-4xl font-bold ${popular ? 'text-blue-900' : 'text-gray-900'}`}
        >
          {price}
        </span>
        <span
          className={`ml-1 text-lg ${popular ? 'text-blue-700' : 'text-gray-600'}`}
        >
          {period}
        </span>
      </div>
      {/* Currency conversion rate */}
      {!free && (
        <p
          className={`mt-1 text-sm ${popular ? 'text-blue-600' : 'text-gray-500'}`}
        >
          ≈ €{Math.round(parseInt(price.replace(/[^\d]/g, '')) / 25)} per month
        </p>
      )}
    </div>

    <ul className="mb-8 flex-grow space-y-4">
      {features.map((feature, index) => (
        <li key={index} className="flex items-start">
          <svg
            className={`mt-0.5 mr-3 h-5 w-5 flex-shrink-0 ${
              popular ? 'text-blue-500' : 'text-green-500'
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          <span
            className={`text-sm ${popular ? 'text-blue-800' : 'text-gray-600'}`}
          >
            {feature}
          </span>
        </li>
      ))}
    </ul>

    <Link
      href={ctaLink}
      className={`block w-full rounded-lg px-4 py-3 text-center text-sm font-medium transition-colors ${
        popular
          ? 'bg-blue-600 text-white hover:bg-blue-700'
          : free
            ? 'bg-gray-900 text-white hover:bg-gray-800'
            : 'bg-gray-900 text-white hover:bg-gray-800'
      }`}
    >
      {cta}
    </Link>
  </div>
);

export default function PricingTiers() {
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: 'Starter',
      price: 'Free',
      period: 'forever',
      description: 'Perfect for trying out Invoice Hub',
      features: [
        '5 invoices per month',
        '2 clients',
        '10 OCR expense receipts/month',
        'Basic invoice template',
        'Email support',
        'Czech legal compliance',
      ],
      cta: 'Start Free',
      ctaLink: '/get-started',
      free: true,
    },
    {
      name: 'Freelancer',
      price: isAnnual ? '169 Kč' : '199 Kč',
      period: '/month',
      description: 'Everything freelancers need to succeed',
      features: [
        'Unlimited invoices',
        'Unlimited clients',
        'Unlimited OCR expense tracking',
        'ARES integration',
        '3 professional templates',
        'Basic dashboard analytics',
        '1 accountant collaboration seat',
        'Email support',
      ],
      cta: 'Start 14-day trial',
      ctaLink: '/get-started?plan=freelancer',
      popular: true,
    },
    {
      name: 'Business',
      price: isAnnual ? '339 Kč' : '399 Kč',
      period: '/month',
      description: 'For growing teams and businesses',
      features: [
        'Everything in Freelancer, plus:',
        'Multi-user access (up to 5 users)',
        'Advanced dashboard & analytics',
        'Custom invoice branding',
        'Recurring invoice automation',
        'Advanced expense categorization',
        '3 accountant collaboration seats',
        'Priority email + chat support',
      ],
      cta: 'Start 14-day trial',
      ctaLink: '/get-started?plan=business',
    },
  ];

  return (
    <section className="w-full bg-white py-16 md:py-24 lg:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl md:text-5xl">
            Choose your plan
          </h2>
          <p className="mt-4 text-lg text-gray-600 md:text-xl">
            Start with our free plan and upgrade as your business grows
          </p>

          <div className="mt-8 flex items-center justify-center">
            <div className="flex items-center rounded-lg bg-gray-100 p-1">
              <button
                onClick={() => setIsAnnual(false)}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                  !isAnnual
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setIsAnnual(true)}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                  isAnnual
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Annual
                <span className="ml-2 rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800">
                  Save 15%
                </span>
              </button>
            </div>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-3 lg:items-stretch">
          {plans.map((plan, index) => (
            <PricingCard key={index} {...plan} />
          ))}
        </div>

        <div className="mt-16 text-center">
          <p className="text-gray-600">
            Need something custom?
            <Link
              href="/contact"
              className="ml-1 font-medium text-blue-600 hover:text-blue-500"
            >
              Contact our sales team
            </Link>
          </p>
        </div>
      </div>
    </section>
  );
}
