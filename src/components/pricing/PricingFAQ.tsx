'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

const FAQItem = ({
  question,
  answer,
}: {
  question: string;
  answer: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const itemId = `faq-${question.replace(/\s+/g, '-').toLowerCase()}`;

  return (
    <div className="border-b border-gray-200">
      <button
        className="flex w-full items-center justify-between py-6 text-left"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-controls={`${itemId}-content`}
        id={`${itemId}-button`}
      >
        <span className="text-lg font-medium text-gray-900">{question}</span>
        <svg
          className={`h-5 w-5 text-gray-500 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      {isOpen && (
        <div
          className="pb-6"
          id={`${itemId}-content`}
          role="region"
          aria-labelledby={`${itemId}-button`}
        >
          <p className="leading-relaxed text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
};

export default function PricingFAQ() {
  const t = useTranslations('Pricing.FAQ');

  const faqKeys = [
    'changePlans',
    'paymentMethods',
    'dataSecure',
    'aresIntegration',
    'accountantAccess',
    'exceedLimits',
    'refunds',
    'setupFee',
  ];

  return (
    <section className="w-full bg-gray-50 py-16 md:py-24 lg:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto max-w-3xl">
          <div className="mb-16 text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {t('title')}
            </h2>
            <p className="mt-4 text-lg text-gray-600">{t('subtitle')}</p>
          </div>

          <div className="space-y-0">
            {faqKeys.map((faqKey, index) => (
              <FAQItem
                key={index}
                question={t(`questions.${faqKey}.question`)}
                answer={t(`questions.${faqKey}.answer`)}
              />
            ))}
          </div>

          <div className="mt-16 text-center">
            <p className="text-gray-600">
              {t('contact.stillHaveQuestions')}
              <Link
                href="/contact"
                className="ml-1 font-medium text-blue-600 hover:text-blue-500"
              >
                {t('contact.contactSupport')}
              </Link>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
