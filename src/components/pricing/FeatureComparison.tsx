'use client';

import React, { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';

const CheckIcon = ({
  className = 'h-5 w-5 text-green-500',
}: {
  className?: string;
}) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 13l4 4L19 7"
    />
  </svg>
);

const XIcon = ({
  className = 'h-5 w-5 text-gray-300',
}: {
  className?: string;
}) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M6 18L18 6M6 6l12 12"
    />
  </svg>
);

export default function FeatureComparison() {
  const [showAllFeatures, setShowAllFeatures] = useState(false);
  const t = useTranslations('Pricing.FeatureComparison');
  const locale = useLocale();

  // Currency formatting based on locale
  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat(locale === 'cz' ? 'cs-CZ' : 'en-US', {
      style: 'currency',
      currency: locale === 'cz' ? 'CZK' : 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const features = [
    {
      category: t('categories.coreFeatures'),
      items: [
        {
          name: t('features.invoicesPerMonth'),
          starter: '5',
          freelancer: t('values.unlimited'),
          business: t('values.unlimited'),
        },
        {
          name: t('features.clients'),
          starter: '2',
          freelancer: t('values.unlimited'),
          business: t('values.unlimited'),
        },
        {
          name: t('features.users'),
          starter: '1',
          freelancer: '1',
          business: '5',
        },
        {
          name: t('features.invoiceTemplates'),
          starter: t('values.basicTemplate'),
          freelancer: t('values.professionalTemplates'),
          business: t('values.customBranding'),
        },
        {
          name: t('features.czechLegalCompliance'),
          starter: true,
          freelancer: true,
          business: true,
        },
      ],
    },
    {
      category: t('categories.automationIntegration'),
      items: [
        {
          name: t('features.aresIntegration'),
          starter: false,
          freelancer: true,
          business: true,
        },
        {
          name: t('features.ocrExpenseTracking'),
          starter: t('values.tenPerMonth'),
          freelancer: t('values.unlimited'),
          business: t('values.unlimited'),
        },
        {
          name: t('features.recurringInvoices'),
          starter: false,
          freelancer: false,
          business: true,
        },
        {
          name: t('features.advancedExpenseCategorization'),
          starter: false,
          freelancer: false,
          business: true,
        },
      ],
    },
    {
      category: t('categories.analyticsReporting'),
      items: [
        {
          name: t('features.basicDashboard'),
          starter: true,
          freelancer: true,
          business: true,
        },
        {
          name: t('features.advancedAnalytics'),
          starter: false,
          freelancer: false,
          business: true,
        },
        {
          name: t('features.cashFlowInsights'),
          starter: false,
          freelancer: false,
          business: true,
        },
        {
          name: t('features.vatReporting'),
          starter: false,
          freelancer: true,
          business: true,
        },
      ],
    },
    {
      category: t('categories.collaboration'),
      items: [
        {
          name: t('features.accountantCollaboration'),
          starter: false,
          freelancer: t('values.oneSeat'),
          business: t('values.threeSeats'),
        },
        {
          name: t('features.multiUserAccess'),
          starter: false,
          freelancer: false,
          business: true,
        },
        {
          name: t('features.teamPermissions'),
          starter: false,
          freelancer: false,
          business: true,
        },
      ],
    },
    {
      category: t('categories.support'),
      items: [
        {
          name: t('features.emailSupport'),
          starter: true,
          freelancer: true,
          business: true,
        },
        {
          name: t('features.prioritySupport'),
          starter: false,
          freelancer: false,
          business: true,
        },
        {
          name: t('features.chatSupport'),
          starter: false,
          freelancer: false,
          business: true,
        },
      ],
    },
  ];

  const displayedFeatures = showAllFeatures ? features : features.slice(0, 2);

  const renderFeatureValue = (value: boolean | string) => {
    if (typeof value === 'boolean') {
      return value ? <CheckIcon /> : <XIcon />;
    }
    return <span className="text-sm text-gray-900">{value}</span>;
  };

  return (
    <section className="w-full bg-white py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto max-w-6xl">
          <div className="mb-16 text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {t('title')}
            </h2>
            <p className="mt-4 text-lg text-gray-600">{t('subtitle')}</p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-gray-200">
                  <th
                    scope="col"
                    className="py-4 pr-4 text-left font-medium text-gray-900"
                  >
                    {t('featuresHeader')}
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-4 text-center font-medium text-gray-900"
                  >
                    <div className="flex flex-col items-center">
                      <span>{t('plans.starter')}</span>
                      <span className="text-sm font-normal text-gray-500">
                        {t('plans.free')}
                      </span>
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-4 text-center font-medium text-gray-900"
                  >
                    <div className="flex flex-col items-center">
                      <span>{t('plans.freelancer')}</span>
                      <span className="text-sm font-normal text-gray-500">
                        {formatPrice(locale === 'cz' ? 199 : 8)}/month
                      </span>
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-4 text-center font-medium text-gray-900"
                  >
                    <div className="flex flex-col items-center">
                      <span>{t('plans.business')}</span>
                      <span className="text-sm font-normal text-gray-500">
                        {formatPrice(locale === 'cz' ? 399 : 16)}/month
                      </span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {displayedFeatures.map((category, categoryIndex) => (
                  <React.Fragment key={categoryIndex}>
                    <tr>
                      <td colSpan={4} className="py-6">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {category.category}
                        </h3>
                      </td>
                    </tr>
                    {category.items.map((feature, featureIndex) => (
                      <tr
                        key={featureIndex}
                        className="border-b border-gray-100"
                      >
                        <td className="py-4 pr-4 text-gray-700">
                          {feature.name}
                        </td>
                        <td className="px-4 py-4 text-center">
                          {renderFeatureValue(feature.starter)}
                        </td>
                        <td className="px-4 py-4 text-center">
                          {renderFeatureValue(feature.freelancer)}
                        </td>
                        <td className="px-4 py-4 text-center">
                          {renderFeatureValue(feature.business)}
                        </td>
                      </tr>
                    ))}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-8 text-center">
            <button
              onClick={() => setShowAllFeatures(!showAllFeatures)}
              className="inline-flex items-center font-medium text-blue-600 hover:text-blue-500"
            >
              {showAllFeatures ? t('toggleShowLess') : t('toggleShowAll')}
              <svg
                className={`ml-1 h-4 w-4 transition-transform ${showAllFeatures ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
