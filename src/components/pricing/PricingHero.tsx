import { useTranslations } from 'next-intl';

export default function PricingHero() {
  const t = useTranslations('Pricing.Hero');

  return (
    <section className="relative w-full bg-gradient-to-br from-gray-50 to-white py-16 md:py-24 lg:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto max-w-4xl text-center">
          <div className="mb-8 inline-flex items-center rounded-full border border-gray-200 bg-white px-4 py-2 text-sm text-gray-600 shadow-sm">
            <span className="mr-2 h-2 w-2 rounded-full bg-green-500"></span>
            {t('badge')}
          </div>

          <h1 className="mb-6 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
            {t('title')}
          </h1>

          <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-600 md:text-xl">
            {t('subtitle')}
          </p>

          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center text-sm text-gray-600">
              <svg
                className="mr-2 h-4 w-4 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              {t('features.aresIntegration')}
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <svg
                className="mr-2 h-4 w-4 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              {t('features.ocrTracking')}
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <svg
                className="mr-2 h-4 w-4 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              {t('features.compliance')}
            </div>
          </div>
        </div>
      </div>

      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-32 h-80 w-80 rounded-full bg-gradient-to-br from-blue-400 to-purple-600 opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-32 h-80 w-80 rounded-full bg-gradient-to-br from-pink-400 to-red-600 opacity-20 blur-3xl"></div>
      </div>
    </section>
  );
}
