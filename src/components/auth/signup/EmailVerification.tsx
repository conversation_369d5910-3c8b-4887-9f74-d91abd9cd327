'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { supabase } from '@/lib/supabase';

interface EmailVerificationProps {
  email: string;
  onVerified?: (userId: string) => void;
  onBack?: () => void;
  onResend?: () => void;
  loading?: boolean;
  error?: string | null;
}

export function EmailVerification({
  email,
  onVerified,
  onBack,
  onResend,
  loading = false,
  error = null,
}: EmailVerificationProps) {
  const t = useTranslations('Auth.Signup');
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [checkingVerification, setCheckingVerification] = useState(false);

  // Start cooldown timer
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  // Listen for auth state changes (email verification)
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user && onVerified) {
        // User has verified their email and is now signed in
        onVerified(session.user.id);
      }
    });

    return () => subscription.unsubscribe();
  }, [onVerified]);

  const handleResendEmail = async () => {
    if (onResend) {
      onResend();
      setResendCooldown(60); // 60 second cooldown
      return;
    }

    setResendLoading(true);

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) {
        console.error('Resend error:', error);
      } else {
        setResendCooldown(60); // 60 second cooldown
      }
    } catch (error) {
      console.error('Resend error:', error);
    } finally {
      setResendLoading(false);
    }
  };

  const handleCheckVerification = async () => {
    setCheckingVerification(true);

    try {
      // First, try to get the current session
      const { data: sessionData } = await supabase.auth.getSession();

      if (sessionData.session?.user) {
        // User is already signed in, check if email is verified
        if (sessionData.session.user.email_confirmed_at && onVerified) {
          onVerified(sessionData.session.user.id);
          return;
        }
      }

      // If no session exists, try to refresh to see if email verification created one
      const { data: refreshData, error: refreshError } =
        await supabase.auth.refreshSession();

      if (refreshData.session?.user?.email_confirmed_at && onVerified) {
        onVerified(refreshData.session.user.id);
      } else if (refreshError) {
        console.error('Refresh error:', refreshError);
      }
    } catch (error) {
      console.error('Check verification error:', error);
    } finally {
      setCheckingVerification(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
          <svg
            className="h-6 w-6 text-green-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
        </div>
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          {t('emailVerification.title')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('emailVerification.subtitle')}
        </p>
        <p className="mt-1 text-sm font-medium text-gray-900">{email}</p>
      </div>

      {/* Error display */}
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {t('emailVerification.error.title')}
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="rounded-md bg-blue-50 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              {t('emailVerification.instructions.title')}
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc space-y-1 pl-5">
                <li>{t('emailVerification.instructions.step1')}</li>
                <li>{t('emailVerification.instructions.step2')}</li>
                <li>{t('emailVerification.instructions.step3')}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col space-y-4">
        <button
          type="button"
          onClick={handleCheckVerification}
          disabled={checkingVerification || loading}
          className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
        >
          {checkingVerification
            ? t('emailVerification.checking')
            : t('emailVerification.checkVerification')}
        </button>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="bg-gray-50 px-2 text-gray-500">
              {t('emailVerification.or')}
            </span>
          </div>
        </div>

        <button
          type="button"
          onClick={handleResendEmail}
          disabled={resendLoading || resendCooldown > 0 || loading}
          className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
        >
          {resendLoading
            ? t('emailVerification.resending')
            : resendCooldown > 0
            ? t('emailVerification.resendCooldown', { seconds: resendCooldown })
            : t('emailVerification.resendEmail')}
        </button>

        {onBack && (
          <button
            type="button"
            onClick={onBack}
            disabled={loading}
            className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          >
            {t('emailVerification.back')}
          </button>
        )}
      </div>
    </div>
  );
}
