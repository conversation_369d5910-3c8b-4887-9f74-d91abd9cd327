'use client';

import { useState } from 'react';
import { SignupFormData } from '@/types/auth';
import { useTranslations } from 'next-intl';

interface ProfileCompletionProps {
  formData: Partial<SignupFormData>;
  onUpdate: (data: Partial<SignupFormData>) => void;
  onComplete: (data: Partial<SignupFormData>) => void;
  onBack: () => void;
  loading: boolean;
  error: string | null;
}

export function ProfileCompletion({
  formData,
  onUpdate,
  onComplete,
  onBack,
  loading,
  error,
}: ProfileCompletionProps) {
  const t = useTranslations('Auth.Signup');
  const [localData, setLocalData] = useState({
    phone: formData.phone || '',
    address: formData.address || '',
    ico: formData.ico || '',
    dic: formData.dic || '',
    is_vat_payer: formData.is_vat_payer || false,
    website: formData.website || '',
    bank_details: formData.bank_details || '',
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const handleInputChange = (field: string, value: string | boolean) => {
    setLocalData((prev) => ({ ...prev, [field]: value }));
    onUpdate({ [field]: value });

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // ICO validation for Czech businesses (optional but if provided, must be valid)
    if (localData.ico && !/^\d{8}$/.test(localData.ico)) {
      errors.ico = t('profileCompletion.validation.icoInvalid');
    }

    // DIC validation for Czech VAT (optional but if provided, must be valid)
    if (localData.dic && !/^CZ\d{8,10}$/.test(localData.dic)) {
      errors.dic = t('profileCompletion.validation.dicInvalid');
    }

    // Phone validation (optional but if provided, must be valid)
    if (localData.phone && !/^\+?[\d\s\-\(\)]+$/.test(localData.phone)) {
      errors.phone = t('profileCompletion.validation.phoneInvalid');
    }

    // Website validation (optional but if provided, must be valid)
    if (localData.website && !/^https?:\/\/.+\..+/.test(localData.website)) {
      errors.website = t('profileCompletion.validation.websiteInvalid');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // All fields are optional for profile completion
    const submitData = {
      phone: localData.phone.trim() || undefined,
      address: localData.address.trim() || undefined,
      ico: localData.ico.trim() || undefined,
      dic: localData.dic.trim() || undefined,
      is_vat_payer: localData.is_vat_payer,
      website: localData.website.trim() || undefined,
      bank_details: localData.bank_details.trim() || undefined,
    };

    onComplete(submitData);
  };

  const handleSkip = () => {
    onComplete({});
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          {t('profileCompletion.title')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('profileCompletion.subtitle')}
        </p>
      </div>

      {/* Error display */}
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {t('profileCompletion.error.title')}
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Phone */}
        <div>
          <label
            htmlFor="phone"
            className="block text-sm font-medium text-gray-700"
          >
            {t('profileCompletion.fields.phone.label')}
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            autoComplete="tel"
            value={localData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
              validationErrors.phone ? 'border-red-300' : 'border-gray-300'
            } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
            placeholder={t('profileCompletion.fields.phone.placeholder')}
          />
          {validationErrors.phone && (
            <p className="mt-1 text-sm text-red-600">{validationErrors.phone}</p>
          )}
        </div>

        {/* Address */}
        <div>
          <label
            htmlFor="address"
            className="block text-sm font-medium text-gray-700"
          >
            {t('profileCompletion.fields.address.label')}
          </label>
          <textarea
            id="address"
            name="address"
            rows={3}
            value={localData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
              validationErrors.address ? 'border-red-300' : 'border-gray-300'
            } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
            placeholder={t('profileCompletion.fields.address.placeholder')}
          />
          {validationErrors.address && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.address}
            </p>
          )}
        </div>

        {/* Czech Business Fields (for company accounts) */}
        {formData.account_type === 'company' && (
          <>
            {/* ICO */}
            <div>
              <label
                htmlFor="ico"
                className="block text-sm font-medium text-gray-700"
              >
                {t('profileCompletion.fields.ico.label')}
              </label>
              <input
                id="ico"
                name="ico"
                type="text"
                value={localData.ico}
                onChange={(e) => handleInputChange('ico', e.target.value)}
                className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
                  validationErrors.ico ? 'border-red-300' : 'border-gray-300'
                } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                placeholder={t('profileCompletion.fields.ico.placeholder')}
              />
              {validationErrors.ico && (
                <p className="mt-1 text-sm text-red-600">
                  {validationErrors.ico}
                </p>
              )}
            </div>

            {/* DIC */}
            <div>
              <label
                htmlFor="dic"
                className="block text-sm font-medium text-gray-700"
              >
                {t('profileCompletion.fields.dic.label')}
              </label>
              <input
                id="dic"
                name="dic"
                type="text"
                value={localData.dic}
                onChange={(e) => handleInputChange('dic', e.target.value)}
                className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
                  validationErrors.dic ? 'border-red-300' : 'border-gray-300'
                } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                placeholder={t('profileCompletion.fields.dic.placeholder')}
              />
              {validationErrors.dic && (
                <p className="mt-1 text-sm text-red-600">
                  {validationErrors.dic}
                </p>
              )}
            </div>

            {/* VAT Payer */}
            <div className="flex items-center">
              <input
                id="is_vat_payer"
                name="is_vat_payer"
                type="checkbox"
                checked={localData.is_vat_payer}
                onChange={(e) => handleInputChange('is_vat_payer', e.target.checked)}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="is_vat_payer" className="ml-2 block text-sm text-gray-900">
                {t('profileCompletion.fields.isVatPayer.label')}
              </label>
            </div>
          </>
        )}

        {/* Website */}
        <div>
          <label
            htmlFor="website"
            className="block text-sm font-medium text-gray-700"
          >
            {t('profileCompletion.fields.website.label')}
          </label>
          <input
            id="website"
            name="website"
            type="url"
            value={localData.website}
            onChange={(e) => handleInputChange('website', e.target.value)}
            className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
              validationErrors.website ? 'border-red-300' : 'border-gray-300'
            } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
            placeholder={t('profileCompletion.fields.website.placeholder')}
          />
          {validationErrors.website && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.website}
            </p>
          )}
        </div>

        {/* Bank Details */}
        <div>
          <label
            htmlFor="bank_details"
            className="block text-sm font-medium text-gray-700"
          >
            {t('profileCompletion.fields.bankDetails.label')}
          </label>
          <textarea
            id="bank_details"
            name="bank_details"
            rows={3}
            value={localData.bank_details}
            onChange={(e) => handleInputChange('bank_details', e.target.value)}
            className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
              validationErrors.bank_details ? 'border-red-300' : 'border-gray-300'
            } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
            placeholder={t('profileCompletion.fields.bankDetails.placeholder')}
          />
          {validationErrors.bank_details && (
            <p className="mt-1 text-sm text-red-600">
              {validationErrors.bank_details}
            </p>
          )}
        </div>

        {/* Buttons */}
        <div className="flex space-x-4">
          <button
            type="button"
            onClick={onBack}
            disabled={loading}
            className="flex flex-1 justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          >
            {t('profileCompletion.back')}
          </button>
          <button
            type="button"
            onClick={handleSkip}
            disabled={loading}
            className="flex flex-1 justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          >
            {t('profileCompletion.skip')}
          </button>
          <button
            type="submit"
            disabled={loading}
            className="flex flex-1 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          >
            {loading ? t('profileCompletion.completing') : t('profileCompletion.complete')}
          </button>
        </div>
      </form>
    </div>
  );
}
