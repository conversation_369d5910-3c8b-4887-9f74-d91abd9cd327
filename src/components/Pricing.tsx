'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useState } from 'react';

const PricingCard = ({
  name,
  price,
  period,
  description,
  features,
  cta,
  popular = false,
}: {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  cta: string;
  popular?: boolean;
}) => (
  <div
    className={`relative rounded-2xl border p-8 ${
      popular
        ? 'border-gray-900 bg-gray-900 text-white shadow-2xl'
        : 'border-gray-200 bg-white shadow-sm'
    }`}
  >
    {popular && (
      <div className="absolute -top-4 left-1/2 -translate-x-1/2">
        <span className="rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-1 text-sm font-medium text-white">
          Most Popular
        </span>
      </div>
    )}

    <div className="mb-8">
      <h3
        className={`text-xl font-semibold ${popular ? 'text-white' : 'text-gray-900'}`}
      >
        {name}
      </h3>
      <p
        className={`mt-2 text-sm ${popular ? 'text-gray-300' : 'text-gray-600'}`}
      >
        {description}
      </p>
    </div>

    <div className="mb-8">
      <div className="flex items-baseline">
        <span
          className={`text-4xl font-bold ${popular ? 'text-white' : 'text-gray-900'}`}
        >
          {price}
        </span>
        <span
          className={`ml-1 text-lg ${popular ? 'text-gray-300' : 'text-gray-600'}`}
        >
          {period}
        </span>
      </div>
    </div>

    <ul className="mb-8 space-y-4">
      {features.map((feature, index) => (
        <li key={index} className="flex items-start">
          <svg
            className={`mt-0.5 mr-3 h-5 w-5 flex-shrink-0 ${
              popular ? 'text-green-400' : 'text-green-500'
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          <span
            className={`text-sm ${popular ? 'text-gray-300' : 'text-gray-600'}`}
          >
            {feature}
          </span>
        </li>
      ))}
    </ul>

    <Link
      href="/get-started"
      className={`block w-full rounded-lg px-4 py-3 text-center text-sm font-medium transition-colors ${
        popular
          ? 'bg-white text-gray-900 hover:bg-gray-100'
          : 'bg-gray-900 text-white hover:bg-gray-800'
      }`}
    >
      {cta}
    </Link>
  </div>
);

export default function Pricing() {
  const t = useTranslations('Landing.Pricing');
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      name: t('starter.name'),
      price: isYearly ? t('starter.yearlyPrice') : t('starter.price'),
      period: t('starter.period'),
      description: t('starter.description'),
      features: t.raw('starter.features') as string[],
      cta: t('starter.cta'),
    },
    {
      name: t('professional.name'),
      price: isYearly ? '$23' : t('professional.price'),
      period: t('professional.period'),
      description: t('professional.description'),
      features: t.raw('professional.features') as string[],
      cta: t('professional.cta'),
      popular: true,
    },
    {
      name: t('enterprise.name'),
      price: t('enterprise.price'),
      period: t('enterprise.period'),
      description: t('enterprise.description'),
      features: t.raw('enterprise.features') as string[],
      cta: t('enterprise.cta'),
    },
  ];

  return (
    <section id="pricing" className="w-full bg-white py-16 md:py-24 lg:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl md:text-5xl">
            {t('title')}
          </h2>
          <p className="mt-4 text-lg text-gray-600 md:text-xl">
            {t('subtitle')}
          </p>

          <div className="mt-8 flex items-center justify-center">
            <div className="flex items-center rounded-lg bg-gray-100 p-1">
              <button
                onClick={() => setIsYearly(false)}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                  !isYearly
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {t('monthly')}
              </button>
              <button
                onClick={() => setIsYearly(true)}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                  isYearly
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {t('yearly')}
                <span className="ml-2 rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800">
                  {t('yearlyDiscount')}
                </span>
              </button>
            </div>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {plans.map((plan, index) => (
            <PricingCard key={index} {...plan} />
          ))}
        </div>
      </div>
    </section>
  );
}
