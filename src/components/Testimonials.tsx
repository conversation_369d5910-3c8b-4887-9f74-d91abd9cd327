import { useTranslations } from 'next-intl';
import { useState } from 'react';
import Image from 'next/image';

const TestimonialCard = ({
  quote,
  author,
  role,
  avatar,
}: {
  quote: string;
  author: string;
  role: string;
  avatar: string;
}) => {
  const [imageError, setImageError] = useState(false);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-white p-8 shadow-sm">
      <div className="mb-6">
        <div className="flex text-yellow-400">
          {[...Array(5)].map((_, i) => (
            <svg key={i} className="h-5 w-5 fill-current" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
        </div>
      </div>

      <blockquote className="mb-6 text-gray-900">
        <p className="text-lg leading-relaxed">&ldquo;{quote}&rdquo;</p>
      </blockquote>

      <div className="flex items-center">
        <div className="mr-4 h-12 w-12 overflow-hidden rounded-full bg-gray-200">
          {!imageError && avatar ? (
            <Image
              src={avatar}
              alt={`${author} avatar`}
              width={48}
              height={48}
              className="h-full w-full object-cover"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-blue-400 to-purple-600 font-semibold text-white">
              {getInitials(author)}
            </div>
          )}
        </div>
        <div>
          <div className="font-semibold text-gray-900">{author}</div>
          <div className="text-sm text-gray-600">{role}</div>
        </div>
      </div>
    </div>
  );
};

export default function Testimonials() {
  const t = useTranslations('Landing.Testimonials');

  const testimonials = [
    {
      quote: t('testimonial1.quote'),
      author: t('testimonial1.author'),
      role: t('testimonial1.role'),
      avatar: t('testimonial1.avatar'),
    },
    {
      quote: t('testimonial2.quote'),
      author: t('testimonial2.author'),
      role: t('testimonial2.role'),
      avatar: t('testimonial2.avatar'),
    },
    {
      quote: t('testimonial3.quote'),
      author: t('testimonial3.author'),
      role: t('testimonial3.role'),
      avatar: t('testimonial3.avatar'),
    },
  ];

  return (
    <section className="w-full bg-gray-50 py-16 md:py-24 lg:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl md:text-5xl">
            {t('title')}
          </h2>
          <p className="mt-4 text-lg text-gray-600 md:text-xl">
            {t('subtitle')}
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard key={index} {...testimonial} />
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="inline-flex items-center space-x-8 text-gray-400">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded bg-gray-300"></div>
              <span className="text-sm font-medium">Company 1</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded bg-gray-300"></div>
              <span className="text-sm font-medium">Company 2</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded bg-gray-300"></div>
              <span className="text-sm font-medium">Company 3</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded bg-gray-300"></div>
              <span className="text-sm font-medium">Company 4</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
