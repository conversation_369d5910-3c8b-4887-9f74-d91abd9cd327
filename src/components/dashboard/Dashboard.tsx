'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { AuthSession } from '@/types/auth';
import { authStorage } from '@/lib/auth-storage';
import { supabase } from '@/lib/supabase';
import { DashboardHeader } from './DashboardHeader';
import { DashboardContent } from './DashboardContent';

export function Dashboard() {
  const router = useRouter();
  const t = useTranslations('Dashboard');
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSession = async () => {
      try {
        // First try to get session from secure storage
        const storedSession = await authStorage.get();
        if (storedSession) {
          setSession(storedSession);
          setLoading(false);
          return;
        }

        // If no stored session, check Supabase auth
        const {
          data: { session: supabaseSession },
        } = await supabase.auth.getSession();

        if (!supabaseSession) {
          // No session found, redirect to login
          router.push('/auth/login');
          return;
        }

        // If we have a Supabase session but no stored session,
        // validate and reconstruct the session
        const response = await fetch('/api/auth/session/validate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${supabaseSession.access_token}`,
          },
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setSession(result.data.session);
            authStorage.store(result.data.session);
          } else {
            router.push('/auth/login');
            return;
          }
        } else {
          router.push('/auth/login');
          return;
        }
      } catch (error) {
        console.error('Error loading session:', error);
        router.push('/auth/login');
        return;
      } finally {
        setLoading(false);
      }
    };

    loadSession();
  }, [router]);

  const handleLogout = async () => {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut();

      // Clear local session storage
      authStorage.clear();

      // Redirect to login page
      router.push('/auth/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Even if there's an error, clear local storage and redirect
      authStorage.clear();
      router.push('/auth/login');
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-indigo-600 border-t-transparent"></div>
          <p className="mt-4 text-sm text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader session={session} onLogout={handleLogout} />
      <DashboardContent session={session} />
    </div>
  );
}
