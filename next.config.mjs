import createNextIntlPlugin from 'next-intl/plugin';

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  experimental: {},
  async redirects() {
    return [
      {
        source: '/get-started',
        destination: '/en/get-started',
        permanent: false,
      },
      {
        source: '/pricing',
        destination: '/en/pricing',
        permanent: false,
      },
      {
        source: '/auth/login',
        destination: '/en/auth/login',
        permanent: false,
      },
      {
        source: '/auth/signup',
        destination: '/en/auth/signup',
        permanent: false,
      },
    ];
  },
};

// Use the proper request config file
const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');
export default withNextIntl(nextConfig);
